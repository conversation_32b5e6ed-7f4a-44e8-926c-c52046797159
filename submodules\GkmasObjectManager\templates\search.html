{% extends "layout.html" %} {% block content %}

<script
    type="text/javascript"
    src="{{ url_for('static', filename='js/search.js') }}"
></script>

<!-- AJAX Interface -->
<script>
    let query = {{ query | tojson }};
    sortState.byID = {{ byID | tojson }};
    sortState.ascending = {{ ascending | tojson }};
    entriesPerPage = {{ entriesPerPage | tojson }};
    currentPage = {{ currentPage | tojson }};
</script>

<div id="loadingBlinker">
    <img id="loadingBlinkerImage" src="" alt="Loading" />
    Searching...
</div>

<div id="searchpageElements" class="below-navbar hide-by-default">
    <div class="row">
        <div class="col-md-12 title">
            <span id="searchResultTitle"></span>
        </div>
    </div>

    <div class="row mt-2">
        <div class="col-md-6 subtitle">
            <span id="searchResultDigest"></span>
        </div>

        <div class="col-md-3">
            <span class="btn-group" role="group">
                <button id="eppMinus" class="btn btn-outline-primary">
                    <i class="bi bi-dash-lg"></i>
                </button>
                <span id="eppValue" class="btn btn-outline-primary fs-5">
                </span>
                <button id="eppPlus" class="btn btn-outline-primary">
                    <i class="bi bi-plus-lg"></i>
                </button>
            </span>
            <span class="text-center mt-1 mx-2">entries / page</span>
        </div>

        <div class="col-md-1 text-end">Sort by:</div>

        <div class="col-md-1">
            <div class="btn-group" role="group">
                <input
                    type="radio"
                    class="btn-check"
                    name="sortByOptions"
                    id="sortByID"
                />
                <label class="btn btn-outline-primary" for="sortByID">ID</label>
                <input
                    type="radio"
                    class="btn-check"
                    name="sortByOptions"
                    id="sortByName"
                />
                <label class="btn btn-outline-primary" for="sortByName">
                    Name
                </label>
            </div>
        </div>

        <div class="col-md-1">
            <div class="btn-group" role="group">
                <input
                    type="radio"
                    class="btn-check"
                    name="ascDescOptions"
                    id="sortAsc"
                />
                <label class="btn btn-outline-primary" for="sortAsc">
                    <i class="bi bi-sort-up"></i>
                </label>
                <input
                    type="radio"
                    class="btn-check"
                    name="ascDescOptions"
                    id="sortDesc"
                />
                <label class="btn btn-outline-primary" for="sortDesc">
                    <i class="bi bi-sort-down"></i>
                </label>
            </div>
        </div>
    </div>

    <div class="row mt-3 gx-3" id="searchEntryCardContainer"></div>

    <nav aria-label="Page navigation">
        <ul
            class="pagination justify-content-center mt-4"
            id="paginationContainer"
        ></ul>
    </nav>
</div>

{% endblock %}
