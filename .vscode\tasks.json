{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Clear test results",
            "type": "shell",
            "command": "powershell \"rm -r tests/output_images\""
        },
        {
            "label": "Make R.py",
            "type": "shell",
            "command": "python ./tools/make_resources.py"
        }
    ]
}