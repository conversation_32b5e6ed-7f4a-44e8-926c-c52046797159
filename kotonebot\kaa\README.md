# kotonebot.tasks
此文件夹下存放的是与具体游戏逻辑相关的脚本。基本上一个任务对应一个脚本。
每个任务的入口函数都应当用 `@task` 装饰器装饰，每个动作函数都应当用 `@action` 装饰器装饰。

一些名词：
* 任务 Task：程序调度的基本单位，是用户可以在软件进行相应配置。例如领取邮箱、完成培育、清空商店等
* 动作 Action：比任务更小的单元。例如在培育中进行一次练习、在竞赛中进行一次挑战等。
> [!NOTE]  
> 与游戏逻辑无关的函数不应当标记为任务或者动作。

所有对设备的操作都应该通过 `kotonebot.backend.context` 模块中的 `device` 对象进行。

## .actions
此文件夹下存放一些任务之间共同的动作。

## R.py
此脚本由软件自动生成，存放了所有的图片资源名称及对应的路径。
在脚本里对图片资源的引用都应该通过 R 获得，避免硬编码字符串。

此脚本不会被提交到 Git 仓库中。因此首次 Clone 后，应当运行 `python ./tools/make_resources.py` 来生成此脚本。
如果你用的是 VSCode，可以通过 Terminal -> Run Task... -> Make R.py 来运行此脚本。