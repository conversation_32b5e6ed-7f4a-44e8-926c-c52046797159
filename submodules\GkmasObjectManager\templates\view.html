{% extends "layout.html" %} {% block content %}

<script
    type="text/javascript"
    src="{{ url_for('static', filename='js/view.js') }}"
></script>

<!-- AJAX Interface -->
<!-- Must make these visible to JS even if no json conversion is involved -->
<script>
    let type = "{{ type }}";
    let info = {{ info | tojson }};
</script>

<div id="viewpageElements" class="below-navbar">
    <div class="row">
        <div class="col-md-12 title">{{ type }} #{{ info.id }}</div>
        <div class="col-md-12 subtitle font-monospace">{{ info.name }}</div>
    </div>

    <div class="row mt-4">
        <div class="col-md-8" id="viewpageLeftColumn">
            <!-- left -->
            <div class="row">
                <div class="col-md-12">
                    <div
                        id="viewMedia"
                        class="media-container media-container-view"
                    >
                        <div class="prog-container">
                            <div class="d-flex justify-content-between">
                                <span class="prog-stage">Starting...</span>
                                <span class="prog-num"
                                    >0 / {{ info.size }}</span
                                >
                            </div>
                            <div class="prog-bar-container">
                                <div class="prog-bar"></div>
                            </div>
                        </div>
                        <div
                            class="hide-by-default media-content media-content-view"
                        ></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4" id="viewpageRightColumn">
            <!-- upper right -->
            <div class="row">
                <div class="col-md-12">
                    <a
                        href="{{ info.raw_url }}"
                        rel="noopener noreferrer"
                        class="btn btn-outline-primary w-100 fs-3"
                    >
                        Download Raw {{ type }}
                    </a>
                </div>
                <div class="col-md-12 mt-3">
                    <a
                        id="downloadConvertedMedia"
                        class="btn btn-primary w-100 fs-3 disabled"
                    >
                        Loading Conversion
                    </a>
                </div>
            </div>

            <!-- lower right -->
            <div class="row mt-5">
                <div class="col-md-12">
                    <table class="table table-bordered table-hover">
                        <tbody class="align-middle">
                            <tr>
                                <th class="lh-sm">Upload Time</th>
                                <td id="uploadTime">Loading...</td>
                            </tr>
                            <tr>
                                <th>Size</th>
                                <td>{{ info.size }}</td>
                            </tr>
                            {% if info.crc %}
                            <tr>
                                <th>CRC</th>
                                <td>{{ info.crc }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>MD5</th>
                                <td class="font-monospace fs-6">
                                    {{ info.md5 }}
                                </td>
                            </tr>
                            <tr>
                                <th colspan="2">Dependencies</th>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <ul>
                                        {% if info.dependencies %} {% for dep in
                                        info.dependencies %}
                                        <li class="font-monospace fs-6">
                                            <a
                                                href="/view/assetbundle/{{ dep.id }}"
                                                >{{ dep.name }}</a
                                            >
                                        </li>
                                        {% endfor %} {% else %} None {% endif %}
                                    </ul>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
