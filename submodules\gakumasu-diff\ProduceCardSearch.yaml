- id: p_card_search-active_skill-deck_all
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_ActiveSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_ActiveSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アクティブスキルカード
    targetId: Label_ActiveSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-active_skill-deck_all-effect_group-visible-exam_full_power-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_ActiveSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_full_power-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果の
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_ActiveSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アクティブスキルカード
    targetId: Label_ActiveSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-active_skill-first-deck-3
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_ActiveSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_First
  cardPositionType: ProduceCardPositionType_Deck
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 3
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札の上から3枚のアクティブスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-active_skill-hand
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_ActiveSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Hand
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札の
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_ActiveSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アクティブスキルカード
    targetId: Label_ActiveSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-active_skill-lost
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_ActiveSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Lost
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 除外にある
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_ActiveSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アクティブスキルカード
    targetId: Label_ActiveSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-active_skill-playing
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_ActiveSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_ActiveSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アクティブスキルカード
    targetId: Label_ActiveSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-active_skill-target
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_ActiveSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_ActiveSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アクティブスキルカード
    targetId: Label_ActiveSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Deck
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札にあるスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-1-p_card-00-act-0_001
  cardRarities: []
  produceCardIds:
  - p_card-00-act-0_001
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アピールの基本
    targetId: p_card-00-act-0_001
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 1枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-1-p_card-00-men-0_003
  cardRarities: []
  produceCardIds:
  - p_card-00-men-0_003
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 表現の基本
    targetId: p_card-00-men-0_003
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 1枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_block-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_block-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamBlock
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気
    targetId: Label_ExamBlock
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_card_play_aggressive-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_card_play_aggressive-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardPlayAggressive
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: やる気
    targetId: Label_ExamCardPlayAggressive_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_concentration-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_concentration-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_concentration-000-effect_group-visible-exam_preservation-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_concentration-000
  - effect_group-visible-exam_preservation-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_full_power-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_full_power-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_full_power-000-effect_group-visible-exam_preservation-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_full_power-000
  - effect_group-visible-exam_preservation-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_lesson_buff-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_buff-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamLessonBuff
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 集中
    targetId: Label_ExamLessonBuff_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_parameter_buff-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_parameter_buff-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamParameterBuff
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 好調
    targetId: Label_ExamParameterBuff
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_preservation-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_preservation-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-effect_group-visible-exam_review-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_review-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamReview
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 好印象
    targetId: Label_ExamReview_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-p_card-03-act-1_040
  cardRarities: []
  produceCardIds:
  - p_card-03-act-1_040
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スターライト
    targetId: p_card-03-act-1_040
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-p_card-03-ido-3_084
  cardRarities: []
  produceCardIds:
  - p_card-03-ido-3_084
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 踏切の先に
    targetId: p_card-03-ido-3_084
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_all-starter
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: starter
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 名前に「基本」を含むスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_grave
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckGrave
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札か捨札にあるスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_grave-1-p_card-03-act-1_040_upgrade1
  cardRarities: []
  produceCardIds:
  - p_card-03-act-1_040
  upgradeCounts:
  - 1
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckGrave
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札か捨札にある
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スターライト+
    targetId: p_card-03-act-1_040
    targetLevel: 1
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 1枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck_grave-idol-unique-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckGrave
  cardSearchTag: idol-unique
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札か捨札にあるアイドル固有スキルカード1枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-deck-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Deck
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札のスキルカード1枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-first-deck-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_First
  cardPositionType: ProduceCardPositionType_Deck
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札の先頭のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-first-deck-5
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_First
  cardPositionType: ProduceCardPositionType_Deck
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 5
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札の先頭から5枚のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-hand
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Hand
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-hand-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Hand
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札のスキルカード1枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-hand-2
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Hand
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 2
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札のスキルカード2枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-hand-3
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Hand
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 3
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札のスキルカード3枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-hold
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Hold
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 保留
    targetId: Label_ProduceCardPositionType_Hold
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: にあるスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-lost
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Lost
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 除外にあるスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-lost-idol-unique-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Lost
  cardSearchTag: idol-unique
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 除外にあるアイドル固有スキルカード1枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-lost-min_max-4_99
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Lost
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_MinMax
  staminaMin: 4
  staminaMax: 99
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 除外にある消費体力が4以上のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-lost-p_card-02-ido-3_104
  cardRarities: []
  produceCardIds:
  - p_card-02-ido-3_104
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Lost
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 除外にある
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: また、飛べる
    targetId: p_card-02-ido-3_104
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-mental_skill-deck_all
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_MentalSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_MentalSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: メンタルスキルカード
    targetId: Label_MentalSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-mental_skill-hand
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_MentalSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Hand
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札の
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_MentalSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: メンタルスキルカード
    targetId: Label_MentalSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-mental_skill-playing
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_MentalSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_MentalSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: メンタルスキルカード
    targetId: Label_MentalSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-mental_skill-target
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_MentalSkill
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_MentalSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: メンタルスキルカード
    targetId: Label_MentalSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-min_max-6_99
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_MinMax
  staminaMin: 6
  staminaMax: 99
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 消費体力が6以上のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-01-act-1_001
  cardRarities: []
  produceCardIds:
  - p_card-01-act-1_001
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ファンサ
    targetId: p_card-01-act-1_001
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-01-act-1_002
  cardRarities: []
  produceCardIds:
  - p_card-01-act-1_002
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 軽い足取り
    targetId: p_card-01-act-1_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-01-act-1_022
  cardRarities: []
  produceCardIds:
  - p_card-01-act-1_022
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 準備運動
    targetId: p_card-01-act-1_022
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-01-act-1_023
  cardRarities: []
  produceCardIds:
  - p_card-01-act-1_023
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 勢い任せ
    targetId: p_card-01-act-1_023
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-01-men-3_006
  cardRarities: []
  produceCardIds:
  - p_card-01-men-3_006
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 国民的アイドル
    targetId: p_card-01-men-3_006
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-02-men-1_030
  cardRarities: []
  produceCardIds:
  - p_card-02-men-1_030
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: リスタート
    targetId: p_card-02-men-1_030
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-02-men-1_031
  cardRarities: []
  produceCardIds:
  - p_card-02-men-1_031
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: えいえいおー
    targetId: p_card-02-men-1_031
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-02-men-1_032
  cardRarities: []
  produceCardIds:
  - p_card-02-men-1_032
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 思い出し笑い
    targetId: p_card-02-men-1_032
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-02-men-3_002
  cardRarities: []
  produceCardIds:
  - p_card-02-men-3_002
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 私がスター
    targetId: p_card-02-men-3_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-03-act-1_038
  cardRarities: []
  produceCardIds:
  - p_card-03-act-1_038
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ジャストアピール
    targetId: p_card-03-act-1_038
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-1-p_card-03-act-1_040
  cardRarities: []
  produceCardIds:
  - p_card-03-act-1_040
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スターライト
    targetId: p_card-03-act-1_040
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_block-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_block-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamBlock
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気
    targetId: Label_ExamBlock
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_card_play_aggressive-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_card_play_aggressive-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardPlayAggressive
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: やる気
    targetId: Label_ExamCardPlayAggressive_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_concentration-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_concentration-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_full_power-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_full_power-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_lesson_buff-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_buff-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamLessonBuff
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 集中
    targetId: Label_ExamLessonBuff_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_parameter_buff_multiple_per_turn-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_parameter_buff_multiple_per_turn-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamParameterBuffMultiplePerTurn
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 絶好調
    targetId: Label_ExamParameterBuffMultiplePerTurn
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_preservation-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_preservation-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_review-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_review-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamReview
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 好印象
    targetId: Label_ExamReview_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-exam_stamina_consumption_down-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_stamina_consumption_down-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaConsumptionDown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 消費体力減少
    targetId: Label_ExamStaminaConsumptionDown
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-effect_group-visible-stamina_recover_fix-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-stamina_recover_fix-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaRecoverFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力回復
    targetId: Label_ExamStaminaRecoverFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-idol-unique
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: idol-unique
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アイドル固有スキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-is_self
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 自身
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-min_max-4_99
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_MinMax
  staminaMin: 4
  staminaMax: 99
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元々の消費体力が4以上のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-p_card-01-ido-1_058
  cardRarities: []
  produceCardIds:
  - p_card-01-ido-1_058
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 初めてのご褒美
    targetId: p_card-01-ido-1_058
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-p_card-01-ido-3_066
  cardRarities: []
  produceCardIds:
  - p_card-01-ido-3_066
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アドレナリン全開
    targetId: p_card-01-ido-3_066
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-p_card-02-act-1_028
  cardRarities: []
  produceCardIds:
  - p_card-02-act-1_028
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ゆるふわおしゃべり
    targetId: p_card-02-act-1_028
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-p_card-02-men-2_052
  cardRarities: []
  produceCardIds:
  - p_card-02-men-2_052
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ひなたぼっこ
    targetId: p_card-02-men-2_052
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-playing-p_card-03-men-1_049
  cardRarities: []
  produceCardIds:
  - p_card-03-men-1_049
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 嬉しい誤算
    targetId: p_card-03-men-1_049
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-r-playing
  cardRarities:
  - ProduceCardRarity_R
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Playing
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカード（R）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-random-random_pool-p_random_pool-all-upgrade_1-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Random
  cardPositionType: ProduceCardPositionType_RandomPool
  cardSearchTag: ""
  produceCardRandomPoolId: p_random_pool-all-upgrade_1
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強化済みスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: p_random_pool-all-upgrade_1
- id: p_card_search-random-random_pool-p_random_pool-rush_idol_unique_set-ssr-upgrade_1-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Random
  cardPositionType: ProduceCardPositionType_RandomPool
  cardSearchTag: ""
  produceCardRandomPoolId: p_random_pool-rush_idol_unique_set-ssr-upgrade_1
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardCreateId
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 生成
    targetId: Label_ExamCardCreateId
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: した強化済み固有スキルカード（SSR）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: p_random_pool-rush_idol_unique_set-ssr-upgrade_1
- id: p_card_search-random-random_pool-p_random_pool-rush-ssr-upgrade_1-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Random
  cardPositionType: ProduceCardPositionType_RandomPool
  cardSearchTag: ""
  produceCardRandomPoolId: p_random_pool-rush-ssr-upgrade_1
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardCreateId
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 生成
    targetId: Label_ExamCardCreateId
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: した強化済み固有スキルカード（SSR）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: p_random_pool-rush-ssr-upgrade_1
- id: p_card_search-random-random_pool-p_random_pool-ssr-upgrade_1-1
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Random
  cardPositionType: ProduceCardPositionType_RandomPool
  cardSearchTag: ""
  produceCardRandomPoolId: p_random_pool-ssr-upgrade_1
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強化済みスキルカード（SSR）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: p_random_pool-ssr-upgrade_1
- id: p_card_search-sr-lost
  cardRarities:
  - ProduceCardRarity_Sr
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Lost
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 除外にあるスキルカード（SR）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-sr-ssr-lost
  cardRarities:
  - ProduceCardRarity_Sr
  - ProduceCardRarity_Ssr
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Lost
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 除外にあるスキルカード（SR,SSR）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-sr-target
  cardRarities:
  - ProduceCardRarity_Sr
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカード（SR）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-ssr-lost
  cardRarities:
  - ProduceCardRarity_Ssr
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Lost
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 除外にあるスキルカード（SSR）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-ssr-target
  cardRarities:
  - ProduceCardRarity_Ssr
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカード（SSR）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target_is_self
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: true
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 自身
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-01-act-1_001
  cardRarities: []
  produceCardIds:
  - p_card-01-act-1_001
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ファンサ
    targetId: p_card-01-act-1_001
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-01-act-1_002
  cardRarities: []
  produceCardIds:
  - p_card-01-act-1_002
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 軽い足取り
    targetId: p_card-01-act-1_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-01-act-1_022
  cardRarities: []
  produceCardIds:
  - p_card-01-act-1_022
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 準備運動
    targetId: p_card-01-act-1_022
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-01-act-1_023
  cardRarities: []
  produceCardIds:
  - p_card-01-act-1_023
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 勢い任せ
    targetId: p_card-01-act-1_023
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-01-men-3_006
  cardRarities: []
  produceCardIds:
  - p_card-01-men-3_006
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 国民的アイドル
    targetId: p_card-01-men-3_006
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-02-men-1_030
  cardRarities: []
  produceCardIds:
  - p_card-02-men-1_030
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: リスタート
    targetId: p_card-02-men-1_030
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-02-men-1_032
  cardRarities: []
  produceCardIds:
  - p_card-02-men-1_032
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 思い出し笑い
    targetId: p_card-02-men-1_032
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-02-men-3_002
  cardRarities: []
  produceCardIds:
  - p_card-02-men-3_002
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 私がスター
    targetId: p_card-02-men-3_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-1-p_card-03-act-1_038
  cardRarities: []
  produceCardIds:
  - p_card-03-act-1_038
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 1
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ジャストアピール
    targetId: p_card-03-act-1_038
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-effect_group-visible-exam_block-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_block-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamBlock
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気
    targetId: Label_ExamBlock
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-effect_group-visible-exam_concentration-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_concentration-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-effect_group-visible-exam_preservation-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_preservation-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-effect_group-visible-exam_review-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_review-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamReview
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 好印象
    targetId: Label_ExamReview_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-effect_group-visible-stamina_recover_fix-000
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds:
  - effect_group-visible-stamina_recover_fix-000
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaRecoverFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力回復
    targetId: Label_ExamStaminaRecoverFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-idol-unique
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: idol-unique
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アイドル固有スキルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-target-p_card-03-men-1_049
  cardRarities: []
  produceCardIds:
  - p_card-03-men-1_049
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories: []
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCard
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 嬉しい誤算
    targetId: p_card-03-men-1_049
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-trouble-deck_all
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_Trouble
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckAll
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Trouble
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: トラブルカード
    targetId: Label_TroubleSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-trouble-deck_grave
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_Trouble
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_DeckGrave
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札と捨札の
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Trouble
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: トラブルカード
    targetId: Label_TroubleSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-trouble-deck-2
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_Trouble
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Deck
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 2
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札の
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Trouble
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: トラブルカード
    targetId: Label_TroubleSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 2枚
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-trouble-hand
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_Trouble
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Hand
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: トラブルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""
- id: p_card_search-trouble-target
  cardRarities: []
  produceCardIds: []
  upgradeCounts: []
  planType: ProducePlanType_Unknown
  cardCategories:
  - ProduceCardCategory_Trouble
  cardStatusType: ProduceCardSearchStatusType_Unknown
  orderType: ProduceCardOrderType_Unknown
  cardPositionType: ProduceCardPositionType_Target
  cardSearchTag: ""
  produceCardRandomPoolId: ""
  limitCount: 0
  staminaMinMaxType: ConditionMinMaxType_Unknown
  staminaMin: 0
  staminaMax: 0
  examEffectType: ProduceExamEffectType_Unknown
  effectGroupIds: []
  isSelf: false
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: トラブルカード
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  produceCardPoolId: ""