# Kotone's Auto Assistant 琴音小助手
## 功能
* 自动日常，包括
    * 领取礼物（邮件）
    * 领取活动费
    * 领取工作奖励并自动重新安排工作
    * 竞赛挑战
    * 领取任务奖励
    * 清理商店
        * 包括 AP 商店和金币商店
        * 可以购买推荐商品，或者指定要购买的物品
* 低配版自动培育
    * 支持 REGULAR 和 PRO 模式
    * 支持指定 P 偶像<sup>1</sup>
    * 支持指定使用增强道具（笔记数量和 Pt 数量提升）
    * 暂时**只能使用自动编成**回忆和支援卡
    * 对于非练习周，如果没有推荐休息，暂时只会按顺序执行活动支给（活動支給）、文化课（授業）、外出（おでかけ）、休息这四种，不支持咨询（相談）
    * 领取技能卡和 P 饮料时，固定领取第一个。若 P 饮料溢出，则不领取

<sup>1</sup> 见后文的注意事项

## 安装
### 模拟器要求
* 分辨率：必须是 1280x720
* 系统版本：Android 10+（Q，API 29），这是游戏的要求
* 已开启游戏加速器或代理且网络通畅

### 普通用户
TODO

### 技术用户
琴音小助手通过 pip 分发，因此你可以执行下面的命令来安装：
```bash
# Python == 3.10
pip install ksaa
```

> 琴音小助手的缩写是“kaa”（**K**otone's **A**uto **A**ssist），
> 但是“kaa”已经被其他包占用了，
> 因此改用“ksaa”（**K**otone'**s** **A**uto **A**ssist）。

不过为了避免依赖冲突，你最好使用 pipx 来安装：
```bash
# 安装 pipx
scoop install pipx
pipx ensurepath
# 安装 kaa
pipx install ksaa
```
（也可以通过 pip 安装 pipx，详见 [pipx 文档](https://github.com/pypa/pipx#on-windows)）

如果你不想使用 pipx，也可以手动创建虚拟环境，并使用普通的 pip 安装。

安装完成后，只需要运行 `kaa` 命令即可启动 GUI 界面：
```bash
kaa
```

需要注意的是，配置文件 `config.json` 会自动在工作目录下生成。
因此你最好每次都在同一个地方运行 kaa，否则可能会出现找不到配置文件的情况。

## 使用
### 配置
TODO

### 注意事项
> [!IMPORTANT]
> 建议**使用亲密度至少为 7 的偶像**进行培育，
> 因为琴音小助手暂时无法处理亲密度提升事件。

## 开源协议
kaa 本体及框架（kotonebot 文件夹）、相关辅助脚本（tools 文件夹）使用 **GPLv3 协议**开源。kaa 开发工具（kotonebot-devtool）、启动器（bootstrap 文件夹）以 **MIT 协议**开源。

kaa 的开发主要用到了以下开源项目：
* [GkmasObjectManager
](https://github.com/AllenHeartcore/GkmasObjectManager)：用于提取游戏图像资源，以 GPLv3 协议开源。
* [gakumasu-diff](https://github.com/vertesan/gakumasu-diff)：游戏数据。

kaa 的开发还参考了以下开源项目：
* [EmulatorExtras](https://github.com/MaaXYZ/EmulatorExtras)：MuMu 与雷电模拟器的截图与控制接口定义。
* [blue_archive_auto_script](https://github.com/pur1fying/blue_archive_auto_script)：MuMu 与雷电模拟器的截图与控制接口的 Python 实现，以及各模拟器的控制实现。

## 免责声明
**请在使用本项目前仔细阅读以下内容。使用本脚本将带来包括但不限于账号被封禁的风险。**

### 总则
本项目（琴音小助手）是一个为游戏 **《学园偶像大师》（学園アイドルマスター）** 设计的自动操作脚本。本项目的创建目的仅为技术学习与研究，并非为了提供商业服务或鼓励不正当的游戏行为。

### 版权声明
本项目所使用的部分资源文件，包括但不限于图像、音频、模型等，其版权归属于其原始权利人。该游戏的开发商为 **QualiArts**，发行商为**万代南梦宫娱乐（Bandai Namco Entertainment Inc.）**。

1.  **权利归属**：本项目中使用的所有相关游戏资源文件的版权、商标权及其他一切知识产权，均归 **QualiArts**、**万代南梦宫娱乐**或其相关权利方所有。

2.  **非官方性质**：本项目为非官方、非商业性质的开源项目。本项目的开发者与 **QualiArts** 及 **万代南梦宫娱乐**没有任何形式的关联、合作或官方授权。

### 核心风险与责任限制
1.  **账号封禁风险**：**您必须清楚地认识到，使用任何形式的第三方自动操作脚本（包括本项目）都有违反《学园偶像大师》的用户协议（利用規約）的潜在风险。游戏运营商有权对使用此类脚本的账号采取惩罚措施，包括但不限于临时或永久封禁账号。对于因使用本脚本而导致的任何账号损失（如封号、数据回滚等），项目作者概不负责。**

> ・对本服务的服务器等进行非法访问、窃取数据、使用使软件进行非法处理的程序、使用工具等获取信息或使用工具等不正当推进游戏的行为。

> ・本サービスのサーバー等への不正アクセス行為、データ窃取行為、ソフトウェアに不正な処理を行わせるプログラムを使用する行為、ツール等を使用して情報を取得する行為またはツール等を使用して不正にゲームを有利に進める行為

2.  **使用限制**：本项目的全部内容**严禁用于任何商业用途或恶意破坏游戏平衡的行为**。任何将本项目用于此类活动的行为，均可能构成对版权方的侵权和对游戏运营商的违约，由此产生的一切法律责任由使用者自行承担。

3.  **无担保与责任限制**：本项目按“原样”提供，不附带任何形式的明示或暗示担保，包括其功能的稳定性、准确性或持续可用性。对于因使用或无法使用本项目而导致的任何直接、间接、偶然、特殊或继发性损害（**包括但不限于账号封禁**），项目作者概不负责。

**继续下载、安装或使用本项目，即表示您已完全阅读、理解并同意承担以上所有风险和条款。如果您不同意，请立即停止使用并删除本项目的所有相关文件。**

## 开发
见 [DEVELOPMENT.md](./docs/DEVELOPMENT.md)

## 贡献
非常欢迎 PR。

你可以从 Github Issue 中选择一个 Issue 解决，或者从下面的路线图里选一个任务讨论。

## 路线图
下面是待实现的功能：  
（带删除线标记的为已完成）

### 培育
* 允许指定领取 P 饮料、P 物品、技能卡的领取选择优先级
* ~~允许指定行动优先级~~
* 自动使用 P 饮料
* ~~支持非凡（アノマリー）属性偶像的自动培育~~
* ~~支持琴音的自动培育~~
* 允许优先选择活动加成高的偶像进行培育
* ~~支持 MASTER 培育~~
* 支持 NIA 培育

### 日常
* ~~竞赛按分数差距优先选择~~
* ~~自动硬币扭蛋（コインガシャ）~~

### 调度
* ~~模拟器启停~~
* 记录任务执行时间与次数，避免重复执行。例如竞赛每天只执行一次
* 常驻运行与自动运行
* ~~命令行接口~~
* 尝试接入 ALAS

### UI
* UI
    * 使用 Flet/Flutter 重写 UI
    * 分离脚本与 UI，允许 UI 与脚本分别独立运行
* 启动器
    * 使用 C# 替换当前的简易 .bat 文件

### 跨平台
* Android 支持
  - [ ] 使用 Python for Android 移植 kaa 到 Android 平台
    - [ ] 解决 native 依赖编译问题
    - [ ] 需要一个适合移动端的 UI
  - [ ] 调用 Shizuku 执行 ADB 命令
  - [ ] 使用 Pyjnius 绕过 ADB ，调用无障碍直接控制设备
* Linux 支持

### 开发工具
* 使用 Konva.js 重构 ImageAnnotation 工具
* 将开发工具通过 VSCode 扩展与 VSCode 整合

### 其他
* 适配汉化版
  - [ ] 需要一个合适的方法自动切换不用语言的资源文件
  - [ ] 需要一个合适的工具来辅助替换模板图片文件
  - [ ] 收集汉化版本的截图