- id: g_effect-aggressive_add-1
  effectType: ProduceCardGrowEffectType_AggressiveAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-aggressive_add-2
  effectType: ProduceCardGrowEffectType_AggressiveAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-aggressive_add-3
  effectType: ProduceCardGrowEffectType_AggressiveAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-aggressive_add-4
  effectType: ProduceCardGrowEffectType_AggressiveAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-aggressive_add-5
  effectType: ProduceCardGrowEffectType_AggressiveAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-1
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-10
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-11
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 11
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-12
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 12
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-13
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 13
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-14
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 14
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-15
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 15
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-16
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 16
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-17
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 17
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-18
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 18
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-19
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 19
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-2
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-20
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 20
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-3
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-4
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-5
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-6
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-7
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-8
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_add-9
  effectType: ProduceCardGrowEffectType_BlockAdd
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-1
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-10
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-11
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 11
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-12
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 12
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-13
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 13
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-14
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 14
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-15
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 15
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-16
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 16
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-17
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 17
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-18
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 18
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-19
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 19
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-2
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-20
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 20
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-3
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-4
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-5
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-6
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-7
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-8
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-block_reduce-9
  effectType: ProduceCardGrowEffectType_BlockReduce
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-1
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-1
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-2
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-2
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-3
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-3
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-4
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-4
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-5
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-5
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-1
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-1
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-2
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-2
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-3
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-3
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-4
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-4
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-5
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-5
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-6
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-6
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-7
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-7
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-8
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-8
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-9
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-9
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-card_status_enchant_change-card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_count_add-1
  effectType: ProduceCardGrowEffectType_CardStatusEnchantChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_count_add-1
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-1
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-10
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-2
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-3
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-4
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-5
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-6
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-7
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-8
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_add-9
  effectType: ProduceCardGrowEffectType_CostAdd
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_aggressive_add-1
  effectType: ProduceCardGrowEffectType_CostAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_aggressive_reduce-1
  effectType: ProduceCardGrowEffectType_CostAggressiveReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_full_power_point_add-1
  effectType: ProduceCardGrowEffectType_CostFullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_full_power_point_reduce-1
  effectType: ProduceCardGrowEffectType_CostFullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_lesson_buff_add-1
  effectType: ProduceCardGrowEffectType_CostLessonBuffAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_lesson_buff_reduce-1
  effectType: ProduceCardGrowEffectType_CostLessonBuffReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_parameter_buff_add-1
  effectType: ProduceCardGrowEffectType_CostParameterBuffAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_parameter_buff_reduce-1
  effectType: ProduceCardGrowEffectType_CostParameterBuffReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_add-1
  effectType: ProduceCardGrowEffectType_CostPenetrateAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_add-2
  effectType: ProduceCardGrowEffectType_CostPenetrateAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_add-3
  effectType: ProduceCardGrowEffectType_CostPenetrateAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_add-4
  effectType: ProduceCardGrowEffectType_CostPenetrateAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_add-5
  effectType: ProduceCardGrowEffectType_CostPenetrateAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_reduce-1
  effectType: ProduceCardGrowEffectType_CostPenetrateReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_reduce-2
  effectType: ProduceCardGrowEffectType_CostPenetrateReduce
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_reduce-3
  effectType: ProduceCardGrowEffectType_CostPenetrateReduce
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_reduce-4
  effectType: ProduceCardGrowEffectType_CostPenetrateReduce
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_penetrate_reduce-5
  effectType: ProduceCardGrowEffectType_CostPenetrateReduce
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_reduce-1
  effectType: ProduceCardGrowEffectType_CostReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_reduce-2
  effectType: ProduceCardGrowEffectType_CostReduce
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_reduce-3
  effectType: ProduceCardGrowEffectType_CostReduce
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_reduce-4
  effectType: ProduceCardGrowEffectType_CostReduce
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_reduce-5
  effectType: ProduceCardGrowEffectType_CostReduce
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_review_add-1
  effectType: ProduceCardGrowEffectType_CostReviewAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-cost_review_reduce-1
  effectType: ProduceCardGrowEffectType_CostReviewReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-1
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-1
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-2
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-2
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-3
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-3
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-4
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-4
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-5
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-deck_all-all-0_0-g_effect-lesson_add-5
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-10
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-10
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-11
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-11
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-3
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-3
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-4
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-4
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-5
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-5
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-6
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-6
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-7
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-7
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-8
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-8
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-9
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hand-all-0_0-g_effect-lesson_add-9
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-10
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-10
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-11
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-11
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-3
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-3
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-4
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-4
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-5
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-5
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-6
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-6
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-7
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-7
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-8
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-8
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-9
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_add_grow_effect-p_card_search-hold-all-0_0-g_effect-lesson_add-9
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_add_grow_effect-000
- id: g_effect-effect_add-e_effect-exam_block_add_multiple_aggressive-0003-0500-01
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_block_add_multiple_aggressive-0003-0500-01
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_block-000
- id: g_effect-effect_add-e_effect-exam_block-0004
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_block-0004
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_block-000
- id: g_effect-effect_add-e_effect-exam_block-0009
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_block-0009
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_block-000
- id: g_effect-effect_add-e_effect-exam_card_move-p_card_search-deck-hold-select-1_1
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck-hold-select-1_1
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-effect_add-e_effect-exam_card_play_aggressive-0002
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_card_play_aggressive-0002
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_card_play_aggressive-000
- id: g_effect-effect_add-e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_card_upgrade-000
- id: g_effect-effect_add-e_effect-exam_concentration-0001
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_concentration-0001
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_concentration-000
- id: g_effect-effect_add-e_effect-exam_effect_timer-0001-01-e_effect-exam_preservation-0001
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_effect_timer-0001-01-e_effect-exam_preservation-0001
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_effect_timer-000
  - effect_group-visible-exam_preservation-000
- id: g_effect-effect_add-e_effect-exam_full_power_point-0002
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_full_power_point-0002
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_full_power-000
- id: g_effect-effect_add-e_effect-exam_lesson_buff-0002
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson_buff-0002
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_buff-000
- id: g_effect-effect_add-e_effect-exam_lesson_depend_block-0300-01
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson_depend_block-0300-01
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_depend_block-000
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_add-e_effect-exam_lesson_depend_exam_card_play_aggressive-1100-01
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson_depend_exam_card_play_aggressive-1100-01
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_depend_block-000
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_add-e_effect-exam_lesson_depend_exam_review-0600-01
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-0600-01
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_depend_exam_review-000
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_add-e_effect-exam_lesson-0006-01
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson-0006-01
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_add-e_effect-exam_lesson-0012-01
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson-0012-01
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_add-e_effect-exam_parameter_buff_multiple_per_turn-02
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-02
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_parameter_buff_multiple_per_turn-000
- id: g_effect-effect_add-e_effect-exam_parameter_buff-02
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_parameter_buff-02
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_parameter_buff-000
- id: g_effect-effect_add-e_effect-exam_playable_value_add-01
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_playable_value_add-01
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_playable_value_add-000
- id: g_effect-effect_add-e_effect-exam_preservation-0001
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_preservation-0001
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_preservation-000
- id: g_effect-effect_add-e_effect-exam_review-0002
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_review-0002
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_review-000
- id: g_effect-effect_add-e_effect-exam_stamina_consumption_down-02
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_stamina_consumption_down-02
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_stamina_consumption_down-000
- id: g_effect-effect_add-e_trigger-exam_turn_timer-1-e_effect-exam_concentration-0001
  effectType: ProduceCardGrowEffectType_EffectAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds:
  - e_trigger-exam_turn_timer-1
  playProduceExamEffectId: e_effect-exam_concentration-0001
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_concentration-000
- id: g_effect-effect_change-e_effect-exam_concentration-0001-e_effect-exam_concentration-0002
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_concentration-0002
  targetPlayProduceExamEffectIds:
  - e_effect-exam_concentration-0001
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_concentration-000
- id: g_effect-effect_change-e_effect-exam_lesson_buff-0005-e_effect-exam_status_enchant-inf-enchant-p_card-01-ido-3_017-enc01
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-p_card-01-ido-3_017-enc01
  targetPlayProduceExamEffectIds:
  - e_effect-exam_lesson_buff-0005
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_status_enchant-000
  - effect_group-visible-exam_lesson_buff-000
- id: g_effect-effect_change-e_effect-exam_lesson-0018-01-e_effect-exam_lesson_add_multiple_parameter_buff-0018-1000-01
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson_add_multiple_parameter_buff-0018-1000-01
  targetPlayProduceExamEffectIds:
  - e_effect-exam_lesson-0018-01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_change-e_effect-exam_lesson-0020-01-e_effect-exam_multiple_lesson_buff_lesson-0020-2000-01
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_multiple_lesson_buff_lesson-0020-2000-01
  targetPlayProduceExamEffectIds:
  - e_effect-exam_lesson-0020-01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_change-e_effect-exam_lesson-0022-01-e_effect-exam_lesson_add_multiple_parameter_buff-0022-1000-01
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson_add_multiple_parameter_buff-0022-1000-01
  targetPlayProduceExamEffectIds:
  - e_effect-exam_lesson-0022-01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_change-e_effect-exam_lesson-0023-01-e_effect-exam_multiple_lesson_buff_lesson-0023-2000-01
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_multiple_lesson_buff_lesson-0023-2000-01
  targetPlayProduceExamEffectIds:
  - e_effect-exam_lesson-0023-01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_change-e_effect-exam_lesson-0025-01-e_effect-exam_lesson_add_multiple_parameter_buff-0025-1000-01
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_lesson_add_multiple_parameter_buff-0025-1000-01
  targetPlayProduceExamEffectIds:
  - e_effect-exam_lesson-0025-01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_change-e_effect-exam_lesson-0026-01-e_effect-exam_multiple_lesson_buff_lesson-0026-2000-01
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_multiple_lesson_buff_lesson-0026-2000-01
  targetPlayProduceExamEffectIds:
  - e_effect-exam_lesson-0026-01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
- id: g_effect-effect_change-e_effect-exam_preservation-0001-e_effect-exam_preservation-0002
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_preservation-0002
  targetPlayProduceExamEffectIds:
  - e_effect-exam_preservation-0001
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_preservation-000
- id: g_effect-full_power_point_add-1
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-10
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-11
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 11
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-12
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 12
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-13
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 13
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-14
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 14
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-15
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 15
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-16
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 16
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-17
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 17
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-18
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 18
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-19
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 19
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-2
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-20
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 20
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-3
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-4
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-5
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-6
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-7
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-8
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_add-9
  effectType: ProduceCardGrowEffectType_FullPowerPointAdd
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-1
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-10
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-11
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 11
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-12
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 12
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-13
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 13
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-14
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 14
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-15
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 15
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-16
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 16
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-17
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 17
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-18
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 18
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-19
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 19
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-2
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-20
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 20
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-3
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-4
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-5
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-6
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-7
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-8
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-full_power_point_reduce-9
  effectType: ProduceCardGrowEffectType_FullPowerPointReduce
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-initial_add
  effectType: ProduceCardGrowEffectType_InitialAdd
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-1
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-10
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-11
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 11
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-12
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 12
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-13
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 13
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-14
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 14
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-15
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 15
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-16
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 16
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-17
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 17
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-18
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 18
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-19
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 19
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-2
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-20
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 20
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-21
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 21
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-22
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 22
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-23
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 23
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-24
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 24
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-25
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 25
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-26
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 26
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-27
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 27
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-28
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 28
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-29
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 29
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-3
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-30
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 30
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-31
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 31
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-32
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 32
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-33
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 33
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-34
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 34
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-35
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 35
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-36
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 36
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-37
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 37
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-38
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 38
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-39
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 39
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-4
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-40
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 40
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-41
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 41
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-42
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 42
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-43
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 43
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-44
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 44
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-45
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 45
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-46
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 46
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-47
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 47
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-48
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 48
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-49
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 49
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-5
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-50
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 50
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-51
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 51
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-52
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 52
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-53
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 53
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-54
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 54
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-55
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 55
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-56
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 56
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-57
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 57
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-58
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 58
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-59
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 59
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-6
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-60
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 60
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-7
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-8
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_add-9
  effectType: ProduceCardGrowEffectType_LessonAdd
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_buff_add-1
  effectType: ProduceCardGrowEffectType_LessonBuffAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_buff_add-2
  effectType: ProduceCardGrowEffectType_LessonBuffAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_buff_add-3
  effectType: ProduceCardGrowEffectType_LessonBuffAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_buff_add-4
  effectType: ProduceCardGrowEffectType_LessonBuffAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_buff_add-5
  effectType: ProduceCardGrowEffectType_LessonBuffAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-1
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-10
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-2
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-3
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-4
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-5
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-6
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-7
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-8
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_add-9
  effectType: ProduceCardGrowEffectType_LessonCountAdd
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-1
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-10
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-2
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-3
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-4
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-5
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-6
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-7
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-8
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_count_reduce-9
  effectType: ProduceCardGrowEffectType_LessonCountReduce
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-100
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 100
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-1000
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 1000
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-150
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 150
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-200
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 200
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-250
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 250
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-300
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 300
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-350
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 350
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-400
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 400
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-450
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 450
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-500
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 500
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-550
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 550
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-600
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 600
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-650
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 650
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-700
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 700
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-750
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 750
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-800
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 800
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-850
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 850
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-900
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 900
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_block_add-950
  effectType: ProduceCardGrowEffectType_LessonDependBlockAdd
  costType: ExamCostType_Unknown
  value: 950
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1000
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1000
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1050
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1050
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1100
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1100
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1150
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1150
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1200
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1200
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1250
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1250
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1300
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1300
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1350
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1350
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1400
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1400
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1450
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1450
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1500
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1500
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1550
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1550
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1600
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1600
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1650
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1650
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1700
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1700
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1750
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1750
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1800
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1800
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1850
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1850
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1900
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1900
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-1950
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 1950
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2000
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2000
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2050
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2050
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2100
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2100
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2150
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2150
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2200
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2200
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2250
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2250
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2300
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2300
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2350
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2350
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2400
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2400
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2450
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2450
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2500
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2500
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2550
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2550
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2600
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2600
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2650
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2650
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2700
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2700
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2750
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2750
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-2800
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 2800
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-500
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 500
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-550
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 550
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-600
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 600
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-650
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 650
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-700
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 700
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-750
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 750
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-800
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 800
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-850
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 850
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-900
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 900
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_card_play_aggressive_add-950
  effectType: ProduceCardGrowEffectType_LessonDependExamCardPlayAggressiveAdd
  costType: ExamCostType_Unknown
  value: 950
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1000
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1000
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1050
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1050
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1100
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1100
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1150
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1150
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1200
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1200
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1250
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1250
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1300
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1300
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1350
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1350
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1400
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1400
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1450
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1450
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1500
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1500
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1550
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1550
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1600
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1600
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1650
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1650
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1700
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1700
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1750
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1750
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1800
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1800
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1850
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1850
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1900
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1900
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-1950
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1950
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-2000
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 2000
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-250
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 250
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-300
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 300
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-350
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 350
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-400
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 400
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-450
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 450
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-500
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 500
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-550
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 550
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-600
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 600
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-650
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 650
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-700
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 700
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-750
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 750
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-800
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 800
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-850
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 850
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-900
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 900
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_depend_exam_review_add-950
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 950
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-1
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-10
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 10
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-11
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 11
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-12
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 12
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-13
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 13
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-14
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 14
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-15
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 15
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-16
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 16
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-17
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 17
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-18
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 18
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-19
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 19
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-2
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-20
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 20
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-3
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-4
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-5
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-6
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 6
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-7
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 7
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-8
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 8
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-lesson_reduce-9
  effectType: ProduceCardGrowEffectType_LessonReduce
  costType: ExamCostType_Unknown
  value: 9
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-p_card-02-act-1_037-enc_1
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_status_enchant-04-enchant-p_card-02-act-1_037-enc02
  targetPlayProduceExamEffectIds:
  - e_effect-exam_status_enchant-04-enchant-p_card-02-act-1_037-enc01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_depend_block-000
  - effect_group-visible-exam_lesson-000
  - effect_group-visible-exam_status_enchant-000
- id: g_effect-p_card-02-act-1_037-enc_2
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_status_enchant-04-enchant-p_card-02-act-1_037-enc03
  targetPlayProduceExamEffectIds:
  - e_effect-exam_status_enchant-04-enchant-p_card-02-act-1_037-enc01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_depend_block-000
  - effect_group-visible-exam_lesson-000
  - effect_group-visible-exam_status_enchant-000
- id: g_effect-p_card-02-act-1_037-enc_3
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_status_enchant-04-enchant-p_card-02-act-1_037-enc04
  targetPlayProduceExamEffectIds:
  - e_effect-exam_status_enchant-04-enchant-p_card-02-act-1_037-enc01
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_depend_block-000
  - effect_group-visible-exam_lesson-000
  - effect_group-visible-exam_status_enchant-000
- id: g_effect-p_card-02-act-3_001-enc_1
  effectType: ProduceCardGrowEffectType_LessonDependExamReviewAdd
  costType: ExamCostType_Unknown
  value: 1600
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-p_card-02-act-3_050-enc_1
  effectType: ProduceCardGrowEffectType_EffectChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-p_card-02-act-3_050-enc03
  targetPlayProduceExamEffectIds:
  - e_effect-exam_status_enchant-inf-enchant-p_card-02-act-3_050-enc02
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds:
  - effect_group-visible-exam_lesson_depend_exam_review-000
  - effect_group-visible-exam_lesson-000
  - effect_group-visible-exam_status_enchant-000
  - effect_group-visible-exam_review-000
- id: g_effect-parameter_buff_multiple_per_turn_add-1
  effectType: ProduceCardGrowEffectType_ParameterBuffMultiplePerTurnAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_multiple_per_turn_add-2
  effectType: ProduceCardGrowEffectType_ParameterBuffMultiplePerTurnAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_multiple_per_turn_add-3
  effectType: ProduceCardGrowEffectType_ParameterBuffMultiplePerTurnAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_multiple_per_turn_add-4
  effectType: ProduceCardGrowEffectType_ParameterBuffMultiplePerTurnAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_multiple_per_turn_add-5
  effectType: ProduceCardGrowEffectType_ParameterBuffMultiplePerTurnAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_turn_add-1
  effectType: ProduceCardGrowEffectType_ParameterBuffTurnAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_turn_add-2
  effectType: ProduceCardGrowEffectType_ParameterBuffTurnAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_turn_add-3
  effectType: ProduceCardGrowEffectType_ParameterBuffTurnAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_turn_add-4
  effectType: ProduceCardGrowEffectType_ParameterBuffTurnAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-parameter_buff_turn_add-5
  effectType: ProduceCardGrowEffectType_ParameterBuffTurnAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-play_effect_trigger_change-e_trigger-none-review_up-15-e_trigger-none-review_up-10
  effectType: ProduceCardGrowEffectType_PlayEffectTriggerChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: e_trigger-none-review_up-10
  targetPlayEffectProduceExamTriggerIds:
  - e_trigger-none-review_up-15
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-play_move_position_type_change-grave
  effectType: ProduceCardGrowEffectType_PlayMovePositionTypeChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Grave
  effectGroupIds: []
- id: g_effect-play_trigger_change-e_trigger-none-block_up-30-e_trigger-none-block_up-15
  effectType: ProduceCardGrowEffectType_PlayTriggerChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: e_trigger-none-block_up-15
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds:
  - e_trigger-none-block_up-30
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-play_trigger_change-e_trigger-none-preservation_change_count_up-2-e_trigger-none-preservation_change_count_up-1
  effectType: ProduceCardGrowEffectType_PlayTriggerChange
  costType: ExamCostType_Unknown
  value: 0
  playProduceExamTriggerId: e_trigger-none-preservation_change_count_up-1
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds:
  - e_trigger-none-preservation_change_count_up-2
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-review_add-1
  effectType: ProduceCardGrowEffectType_ReviewAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-review_add-2
  effectType: ProduceCardGrowEffectType_ReviewAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-review_add-3
  effectType: ProduceCardGrowEffectType_ReviewAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-review_add-4
  effectType: ProduceCardGrowEffectType_ReviewAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-review_add-5
  effectType: ProduceCardGrowEffectType_ReviewAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-stamina_consumption_down_turn_add-1
  effectType: ProduceCardGrowEffectType_StaminaConsumptionDownTurnAdd
  costType: ExamCostType_Unknown
  value: 1
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-stamina_consumption_down_turn_add-2
  effectType: ProduceCardGrowEffectType_StaminaConsumptionDownTurnAdd
  costType: ExamCostType_Unknown
  value: 2
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-stamina_consumption_down_turn_add-3
  effectType: ProduceCardGrowEffectType_StaminaConsumptionDownTurnAdd
  costType: ExamCostType_Unknown
  value: 3
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-stamina_consumption_down_turn_add-4
  effectType: ProduceCardGrowEffectType_StaminaConsumptionDownTurnAdd
  costType: ExamCostType_Unknown
  value: 4
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []
- id: g_effect-stamina_consumption_down_turn_add-5
  effectType: ProduceCardGrowEffectType_StaminaConsumptionDownTurnAdd
  costType: ExamCostType_Unknown
  value: 5
  playProduceExamTriggerId: ""
  playEffectProduceExamTriggerId: ""
  targetPlayEffectProduceExamTriggerIds: []
  playProduceExamEffectId: ""
  targetPlayProduceExamEffectIds: []
  produceCardStatusEnchantId: ""
  playMovePositionType: ProduceCardMovePositionType_Unknown
  effectGroupIds: []