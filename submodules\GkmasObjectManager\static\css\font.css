@font-face {
    font-family: "NYC Sans";
    src: url("https://hex.xyz/NYC_Sans/NYC_Sans_v0.3_Variable.woff2")
        format("woff2");
}

@font-face {
    font-family: "OpenAI Sans";
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-Light.woff2")
        format("woff2");
    font-weight: 300;
}

@font-face {
    font-family: "OpenAI Sans";
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-LightItalic.woff2")
        format("woff2");
    font-weight: 300;
    font-style: italic;
}

@font-face {
    font-family: "OpenAI Sans";
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-Regular.woff2")
        format("woff2");
    font-weight: 400;
}

@font-face {
    font-family: "OpenAI Sans";
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-RegularItalic.woff2")
        format("woff2");
    font-weight: 400;
    font-style: italic;
}

@font-face {
    font-family: "OpenAI Sans";
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-Medium.woff2")
        format("woff2");
    font-weight: 500;
}

@font-face {
    font-family: "OpenAI Sans";
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-MediumItalic.woff2")
        format("woff2");
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: "OpenAI Sans";
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-Bold.woff2")
        format("woff2");
    font-weight: 700;
}

@font-face {
    font-family: "OpenAI Sans";
    src: url("https://cdn.openai.com/common/fonts/openai-sans/OpenAISans-BoldItalic.woff2")
        format("woff2");
    font-weight: 700;
    font-style: italic;
}
