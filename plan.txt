# while 循环改用 Loop 重构计划
这个脚本项目中的许多逻辑由类似下面的 while 循环构成：
it = Interval(0.5)
while True:
    device.screenshot()
    it.wait()
    if ...:
        # do something
    elif ...:
        # do something
    elif ...:
        # do something
        break
    else:
        # do something

你需要用 loop.py 中的 Loop 类来重构这些逻辑。
重构后：
for _ in Loop(interval=0.3) # interval默认值为 0.3，一般情况下不需要写这个参数
    # device.screenshot() 会自动截图，不需要这句话
    # it.wait() 会自动等待，不需要这句话
    # 剩余逻辑保持不变
    if ...:
        # do something
    elif ...:
        # do something
    elif ...:
        # do something
        break
    else:
        # do something

你要做的是搜索 @kotonebot.kaa.task 下的所有文件里的 while True（大部分条件为 True，少部分为其他），并重构。
这个任务可能及其庞大，你需要先列出所有需要修改的文件，写入 ./memo.txt 文件作为记录，记录每个文件的修改进度，以及其他的一些必要备注等。