- mainTaskGroupId: main_task_group-01-01
  number: 1
  title: 初星課題1
  description: プロデュースに挑戦しよう
  homeDescription: プロデュースに挑戦しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#1"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: ""
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 650
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 20
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 2
  title: 初星課題2
  description: 1章1話をみよう
  homeDescription: 1章1話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-01
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#2"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#2"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 20
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_SupportCardLevelUpdate
- mainTaskGroupId: main_task_group-01-01
  number: 3
  title: 初星課題3
  description: サポートカードを強化しよう
  homeDescription: サポートカードを強化しよう
  missionType: MissionType_IncrementSupportCardLevelUpdateCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#3"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#3"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 650
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 40
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 4
  title: 初星課題4
  description: 名刺を編集しよう
  homeDescription: 名刺を編集しよう
  missionType: MissionType_IncrementMeishiUpdateCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#4"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#4"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 650
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 40
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 5
  title: 初星課題5
  description: 他のプロデューサーをフォローしよう
  homeDescription: 他のプロデューサーをフォローしよう
  missionType: MissionType_AbsoluteFollowCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#5"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#5"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 40
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 6
  title: 初星課題6
  description: 研修を1つクリアしよう
  homeDescription: 研修を1つクリアしよう
  missionType: MissionType_AbsoluteSeminarExamClear
  targetIds1:
  - seminar_gruop-01_01
  - seminar_gruop-01_02
  - seminar_gruop-01_03
  - seminar_gruop-01_04
  - seminar_gruop-01_05
  - seminar_gruop-01_06
  - seminar_gruop-01_07
  - seminar_gruop-01_08
  - seminar_gruop-01_09
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#6"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#6"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 40
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 7
  title: 初星課題7
  description: 試験研修を1つクリアしよう
  homeDescription: 試験研修を1つクリアしよう
  missionType: MissionType_AbsoluteSeminarExamClear
  targetIds1:
  - seminar_gruop-03_01
  - seminar_gruop-03_02
  - seminar_gruop-03_03
  - seminar_gruop-03_04
  - seminar_gruop-03_05
  - seminar_gruop-03_06
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#7"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#7"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 40
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 8
  title: 初星課題8
  description: プロデュースに挑戦しよう
  homeDescription: プロデュースに挑戦しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#8"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#8"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 40
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 9
  title: 初星課題9
  description: 1章2話をみよう
  homeDescription: 1章2話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-02
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#9"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#9"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 40
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_IdolCardLevelLimitRankUpdate
- mainTaskGroupId: main_task_group-01-01
  number: 10
  title: 初星課題10
  description: プロデュースアイドルを特訓しよう
  homeDescription: プロデュースアイドルを特訓しよう
  missionType: MissionType_IncrementIdolCardLevelLimitRankUpdateCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#10"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#10"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 40
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_MoneyReceive
- mainTaskGroupId: main_task_group-01-01
  number: 11
  title: 初星課題11
  description: 活動費を回収しよう
  homeDescription: 活動費を回収しよう
  missionType: MissionType_IncrementReceiveMoney
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#11"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#11"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_DailyExchange
- mainTaskGroupId: main_task_group-01-01
  number: 12
  title: 初星課題12
  description: マニー交換所で交換しよう
  homeDescription: マニー交換所で交換しよう
  missionType: MissionType_IncrementDailyExchangeCount
  targetIds1:
  - exchange-money-1
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#12"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#12"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 13
  title: 初星課題13
  description: プロデュースに挑戦しよう
  homeDescription: プロデュースに挑戦しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#13"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#13"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 14
  title: 初星課題14
  description: 1章3話をみよう
  homeDescription: 1章3話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-03
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#14"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#14"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Work
- mainTaskGroupId: main_task_group-01-01
  number: 15
  title: 初星課題15
  description: お仕事を開始しよう
  homeDescription: お仕事を開始しよう
  missionType: MissionType_IncrementWorkCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#15"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#15"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 16
  title: 初星課題16
  description: |-
    プロデュースに挑戦しよう
    【条件】プラン：センスのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦しよう
    【条件】プラン：センスのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan1
  targetValue: 0
  missionId: "main_task_group-01-01#16"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#16"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_CoinGasha
- mainTaskGroupId: main_task_group-01-01
  number: 17
  title: 初星課題17
  description: センスガシャを1回引こう
  homeDescription: センスガシャを1回引こう
  missionType: MissionType_IncrementShopCoinGashaDrawCount
  targetIds1:
  - coin_gasha-a
  - coin_gasha-a_01
  - coin_gasha-a_02
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#17"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#17"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 18
  title: 初星課題18
  description: 1章4話をみよう
  homeDescription: 1章4話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-04
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#18"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#18"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 19
  title: 初星課題19
  description: サポートカード{threshold}枚をLv20にしよう
  homeDescription: サポートカード{threshold}枚をLv20にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 20
  missionId: "main_task_group-01-01#19"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#19"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 20
  title: 初星課題20
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#20"
  threshold: 15
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#20"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 100
  additionalRewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 21
  title: 初星課題21
  description: |-
    プロデュースに挑戦しよう
    【条件】プラン：ロジックのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦しよう
    【条件】プラン：ロジックのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan2
  targetValue: 0
  missionId: "main_task_group-01-01#21"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#21"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 22
  title: 初星課題22
  description: ロジックガシャを1回引こう
  homeDescription: ロジックガシャを1回引こう
  missionType: MissionType_IncrementShopCoinGashaDrawCount
  targetIds1:
  - coin_gasha-b
  - coin_gasha-b_01
  - coin_gasha-b_02
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#22"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#22"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 23
  title: 初星課題23
  description: 1章5話をみよう
  homeDescription: 1章5話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-05
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#23"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#23"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 24
  title: 初星課題24
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#24"
  threshold: 8
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#24"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 25
  title: 初星課題25
  description: 異なるアイドル{threshold}人のプロデュースを完了しよう
  homeDescription: 異なるアイドル{threshold}人のプロデュースを完了しよう
  missionType: MissionType_AbsoluteProducePlayCharacterCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#25"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#25"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_PvpRate
- mainTaskGroupId: main_task_group-01-01
  number: 26
  title: 初星課題26
  description: コンテストで編成しよう
  homeDescription: コンテストで編成しよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#26"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#26"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 27
  title: 初星課題27
  description: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  homeDescription: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#27"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#27"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 28
  title: 初星課題28
  description: 1章6話をみよう
  homeDescription: 1章6話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-06
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#28"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#28"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 29
  title: 初星課題29
  description: サポートカード{threshold}枚をLv20にしよう
  homeDescription: サポートカード{threshold}枚をLv20にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 20
  missionId: "main_task_group-01-01#29"
  threshold: 2
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#29"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 30
  title: 初星課題30
  description: プロデュースに挑戦して評価C+以上を獲得しよう
  homeDescription: プロデュースに挑戦して評価C+以上を獲得しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 5
  missionId: "main_task_group-01-01#30"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#30"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 31
  title: 初星課題31
  description: 1章7話をみよう
  homeDescription: 1章7話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-07
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#31"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#31"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 32
  title: 初星課題32
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#32"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#32"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Guild
- mainTaskGroupId: main_task_group-01-01
  number: 33
  title: 初星課題33
  description: 研修の【応用１】プラン：センスをクリアしよう
  homeDescription: 研修の【応用１】プラン：センスをクリアしよう
  missionType: MissionType_AbsoluteSeminarExamClear
  targetIds1:
  - seminar_gruop-02_01
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#33"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#33"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 34
  title: 初星課題34
  description: |-
    プロデュースに挑戦して評価C+以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価C+以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan1
  targetValue: 5
  missionId: "main_task_group-01-01#34"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#34"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 35
  title: 初星課題35
  description: 1章8話をみよう
  homeDescription: 1章8話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-08
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#35"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#35"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 36
  title: 初星課題36
  description: 研修の【応用２】プラン：ロジックをクリアしよう
  homeDescription: 研修の【応用２】プラン：ロジックをクリアしよう
  missionType: MissionType_AbsoluteSeminarExamClear
  targetIds1:
  - seminar_gruop-02_02
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#36"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#36"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 37
  title: 初星課題37
  description: |-
    プロデュースに挑戦して評価C+以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価C+以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan2
  targetValue: 5
  missionId: "main_task_group-01-01#37"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#37"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 38
  title: 初星課題38
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#38"
  threshold: 30
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#38"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 39
  title: 初星課題39
  description: 1章9話をみよう
  homeDescription: 1章9話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-09
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#39"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#39"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 40
  title: 初星課題40
  description: プロデュースアイドル{threshold}人の特訓段階を2にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を2にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 2
  missionId: "main_task_group-01-01#40"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#40"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 150
  additionalRewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 41
  title: 初星課題41
  description: プロデュースで定期公演『初』：プロに{threshold}回挑戦しよう
  homeDescription: プロデュースで定期公演『初』：プロに{threshold}回挑戦しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2:
  - produce-002
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#41"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#41"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 42
  title: 初星課題42
  description: サポートカード{threshold}枚をLv20にしよう
  homeDescription: サポートカード{threshold}枚をLv20にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 20
  missionId: "main_task_group-01-01#42"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#42"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 43
  title: 初星課題43
  description: 1章10話をみよう
  homeDescription: 1章10話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-10
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#43"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#43"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 44
  title: 初星課題44
  description: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  homeDescription: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#44"
  threshold: 7
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#44"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 45
  title: 初星課題45
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#45"
  threshold: 13
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#45"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 46
  title: 初星課題46
  description: プロデュースでいずれかのパラメータを600以上に育成しよう
  homeDescription: プロデュースでいずれかのパラメータを600以上に育成しよう
  missionType: MissionType_ProduceConditionClear
  targetIds1:
  - p_cd-any_vodavi-600
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#46"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#46"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 47
  title: 初星課題47
  description: 1章11話をみよう
  homeDescription: 1章11話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-11
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#47"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#47"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 48
  title: 初星課題48
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#48"
  threshold: 25000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#48"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 49
  title: 初星課題49
  description: |-
    プロデュースに挑戦して評価B以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価B以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan1
  targetValue: 6
  missionId: "main_task_group-01-01#49"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#49"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 200
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 50
  title: 初星課題50
  description: |-
    プロデュースに挑戦して評価B以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価B以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan2
  targetValue: 6
  missionId: "main_task_group-01-01#50"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#50"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 51
  title: 初星課題51
  description: 1章12話をみよう
  homeDescription: 1章12話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-12
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#51"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#51"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 52
  title: 初星課題52
  description: サポートカードのLvを{threshold}にしよう
  homeDescription: サポートカードのLvを{threshold}にしよう
  missionType: MissionType_AbsoluteSupportCardLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#52"
  threshold: 30
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#52"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 53
  title: 初星課題53
  description: 最終試験で1位になろう
  homeDescription: 最終試験で1位になろう
  missionType: MissionType_ProduceConditionClear
  targetIds1:
  - p_cd_final_1st
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#53"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#53"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 54
  title: 初星課題54
  description: 1章13話をみよう
  homeDescription: 1章13話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-13
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#54"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#54"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 55
  title: 初星課題55
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#55"
  threshold: 15
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#55"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 56
  title: 初星課題56
  description: プロデュースアイドル{threshold}人の特訓段階を3にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を3にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 3
  missionId: "main_task_group-01-01#56"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#56"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 57
  title: 初星課題57
  description: プロデュースでいずれかのパラメータを800以上に育成しよう
  homeDescription: プロデュースでいずれかのパラメータを800以上に育成しよう
  missionType: MissionType_ProduceConditionClear
  targetIds1:
  - p_cd-any_vodavi-800
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#57"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#57"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 58
  title: 初星課題58
  description: 1章14話をみよう
  homeDescription: 1章14話をみよう
  missionType: MissionType_AbsoluteStoryRead
  targetIds1:
  - story-main-01-01-14
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#58"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#58"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 250
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 59
  title: 初星課題59
  description: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  homeDescription: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-01-01#59"
  threshold: 9
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#59"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-01-01
  number: 60
  title: 初星課題60
  description: TrueEndをみよう
  homeDescription: TrueEndをみよう
  missionType: MissionType_AbsoluteProduceCharacterEnding
  targetIds1: []
  targetIds2: []
  targetIds3:
  - TrueEnd
  targetValue: 0
  missionId: "main_task_group-01-01#60"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-01-01#60"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards:
  - resourceType: ResourceType_Item
    resourceId: item-gashaticket-ssr-fix_1
    quantity: 1
  unlockFeatureTutorialType: TutorialType_Tower
- mainTaskGroupId: main_task_group-02-01
  number: 1
  title: P課題1
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#1"
  threshold: 28000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#1"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 2
  title: P課題2
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#2"
  threshold: 60
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#2"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 3
  title: P課題3
  description: プロデュースでいずれかのパラメータを1000以上に育成しよう
  homeDescription: プロデュースでいずれかのパラメータを1000以上に育成しよう
  missionType: MissionType_ProduceConditionClear
  targetIds1:
  - p_cd-any_vodavi-1000
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#3"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#3"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 4
  title: P課題4
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#4"
  threshold: 6000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#4"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 5
  title: P課題5
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#5"
  threshold: 20
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#5"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 6
  title: P課題6
  description: プロデュースアイドル{threshold}人の特訓段階を3にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を3にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 3
  missionId: "main_task_group-02-01#6"
  threshold: 2
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#6"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 7
  title: P課題7
  description: SPレッスンを{threshold}回クリアしよう
  homeDescription: SPレッスンを{threshold}回クリアしよう
  missionType: MissionType_IncrementProduceLessonClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - LessonVocalSp
  - LessonDanceSp
  - LessonVisualSp
  targetValue: 0
  missionId: "main_task_group-02-01#7"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#7"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 8
  title: P課題8
  description: サポートカード{threshold}枚をLv30にしよう
  homeDescription: サポートカード{threshold}枚をLv30にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 30
  missionId: "main_task_group-02-01#8"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#8"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 9
  title: P課題9
  description: スキルカードを{threshold}種類獲得しよう
  homeDescription: スキルカードを{threshold}種類獲得しよう
  missionType: MissionType_AbsoluteProducePictureBookProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#9"
  threshold: 100
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#9"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 10
  title: P課題10
  description: プロデュースに挑戦して評価B+以上を獲得しよう
  homeDescription: プロデュースに挑戦して評価B+以上を獲得しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 7
  missionId: "main_task_group-02-01#10"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#10"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 11
  title: P課題11
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#11"
  threshold: 80
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#11"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 12
  title: P課題12
  description: お仕事を合計{threshold}時間しよう
  homeDescription: お仕事を合計{threshold}時間しよう
  missionType: MissionType_IncrementWorkDurationHour
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#12"
  threshold: 20
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#12"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 13
  title: P課題13
  description: |-
    プロデュースに挑戦して評価B+以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価B+以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan1
  targetValue: 7
  missionId: "main_task_group-02-01#13"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#13"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 14
  title: P課題14
  description: |-
    プロデュースに挑戦して評価B+以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価B+以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan2
  targetValue: 7
  missionId: "main_task_group-02-01#14"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#14"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 15
  title: P課題15
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#15"
  threshold: 23
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#15"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 16
  title: P課題16
  description: プロデュースアイドル{threshold}人の特訓段階を3にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を3にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 3
  missionId: "main_task_group-02-01#16"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#16"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 17
  title: P課題17
  description: TrueEndアチーブメントを{threshold}個達成
  homeDescription: TrueEndアチーブメントを{threshold}個達成
  missionType: MissionType_AbsoluteProduceCharacterEnding
  targetIds1: []
  targetIds2: []
  targetIds3:
  - TrueEnd
  targetValue: 0
  missionId: "main_task_group-02-01#17"
  threshold: 2
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#17"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 18
  title: P課題18
  description: サポートカードのLvを{threshold}にしよう
  homeDescription: サポートカードのLvを{threshold}にしよう
  missionType: MissionType_AbsoluteSupportCardLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#18"
  threshold: 40
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#18"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 19
  title: P課題19
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#19"
  threshold: 100
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#19"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 20
  title: P課題20
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#20"
  threshold: 25
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#20"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 21
  title: P課題21
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#21"
  threshold: 8000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#21"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 22
  title: P課題22
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#22"
  threshold: 30000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#22"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 23
  title: P課題23
  description: スキルカードを{threshold}種類獲得しよう
  homeDescription: スキルカードを{threshold}種類獲得しよう
  missionType: MissionType_AbsoluteProducePictureBookProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#23"
  threshold: 120
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#23"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 24
  title: P課題24
  description: サポートカード{threshold}枚をLv30にしよう
  homeDescription: サポートカード{threshold}枚をLv30にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 30
  missionId: "main_task_group-02-01#24"
  threshold: 4
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#24"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 25
  title: P課題25
  description: プロデュースアイドル{threshold}人の特訓段階を4にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を4にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 4
  missionId: "main_task_group-02-01#25"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#25"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 26
  title: P課題26
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#26"
  threshold: 120
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#26"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 27
  title: P課題27
  description: コンテストに挑戦しよう
  homeDescription: コンテストに挑戦しよう
  missionType: MissionType_IncrementPvpRatePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#27"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#27"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 28
  title: P課題28
  description: TrueEndアチーブメントを{threshold}個達成
  homeDescription: TrueEndアチーブメントを{threshold}個達成
  missionType: MissionType_AbsoluteProduceCharacterEnding
  targetIds1: []
  targetIds2: []
  targetIds3:
  - TrueEnd
  targetValue: 0
  missionId: "main_task_group-02-01#28"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#28"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 29
  title: P課題29
  description: サポートカード{threshold}枚をLv30にしよう
  homeDescription: サポートカード{threshold}枚をLv30にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 30
  missionId: "main_task_group-02-01#29"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#29"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 30
  title: P課題30
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：花海 咲季
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：花海 咲季
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - hski
  targetValue: 0
  missionId: "main_task_group-02-01#30"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#30"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 31
  title: P課題31
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#31"
  threshold: 10000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#31"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 32
  title: P課題32
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：月村 手毬
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：月村 手毬
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - ttmr
  targetValue: 0
  missionId: "main_task_group-02-01#32"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#32"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 33
  title: P課題33
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#33"
  threshold: 130
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#33"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 34
  title: P課題34
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：藤田 ことね
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：藤田 ことね
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - fktn
  targetValue: 0
  missionId: "main_task_group-02-01#34"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#34"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 35
  title: P課題35
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#35"
  threshold: 27
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#35"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 36
  title: P課題36
  description: Pドリンクを{threshold}種類獲得しよう
  homeDescription: Pドリンクを{threshold}種類獲得しよう
  missionType: MissionType_AbsoluteProducePictureBookProduceDrinkCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#36"
  threshold: 15
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#36"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 37
  title: P課題37
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：有村 麻央
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：有村 麻央
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - amao
  targetValue: 0
  missionId: "main_task_group-02-01#37"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#37"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 38
  title: P課題38
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#38"
  threshold: 32000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#38"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 39
  title: P課題39
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：姫崎 莉波
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：姫崎 莉波
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - hrnm
  targetValue: 0
  missionId: "main_task_group-02-01#39"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#39"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 40
  title: P課題40
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#40"
  threshold: 140
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#40"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 41
  title: P課題41
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：葛城 リーリヤ
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：葛城 リーリヤ
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - kllj
  targetValue: 0
  missionId: "main_task_group-02-01#41"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#41"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 42
  title: P課題42
  description: コンテストに挑戦しよう
  homeDescription: コンテストに挑戦しよう
  missionType: MissionType_IncrementPvpRatePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#42"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#42"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 43
  title: P課題43
  description: プロデュースアイドル{threshold}人の特訓段階を4にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を4にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 4
  missionId: "main_task_group-02-01#43"
  threshold: 2
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#43"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 44
  title: P課題44
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：紫雲 清夏
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：紫雲 清夏
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - ssmk
  targetValue: 0
  missionId: "main_task_group-02-01#44"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#44"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 45
  title: P課題45
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#45"
  threshold: 150
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#45"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 46
  title: P課題46
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：篠澤 広
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：篠澤 広
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - shro
  targetValue: 0
  missionId: "main_task_group-02-01#46"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#46"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 47
  title: P課題47
  description: サポートカード{threshold}枚をLv40にしよう
  homeDescription: サポートカード{threshold}枚をLv40にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 40
  missionId: "main_task_group-02-01#47"
  threshold: 2
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#47"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 48
  title: P課題48
  description: |-
    プロデュースで最終試験に合格しよう
    【条件】：倉本 千奈
  homeDescription: |-
    プロデュースで最終試験に合格しよう
    【条件】：倉本 千奈
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - kcna
  targetValue: 0
  missionId: "main_task_group-02-01#48"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#48"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 49
  title: P課題49
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#49"
  threshold: 12000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#49"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 50
  title: P課題50
  description: スキルカードを{threshold}種類獲得しよう
  homeDescription: スキルカードを{threshold}種類獲得しよう
  missionType: MissionType_AbsoluteProducePictureBookProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#50"
  threshold: 150
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#50"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 51
  title: P課題51
  description: プロデュースに挑戦して評価A以上を獲得しよう
  homeDescription: プロデュースに挑戦して評価A以上を獲得しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 8
  missionId: "main_task_group-02-01#51"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#51"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 52
  title: P課題52
  description: お仕事を合計{threshold}時間しよう
  homeDescription: お仕事を合計{threshold}時間しよう
  missionType: MissionType_IncrementWorkDurationHour
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#52"
  threshold: 20
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#52"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 53
  title: P課題53
  description: プロデュースアイドル{threshold}人の特訓段階を4にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を4にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 4
  missionId: "main_task_group-02-01#53"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#53"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 54
  title: P課題54
  description: |-
    プロデュースに挑戦して評価A以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価A以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan1
  targetValue: 8
  missionId: "main_task_group-02-01#54"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#54"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 55
  title: P課題55
  description: |-
    プロデュースに挑戦して評価A以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価A以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan2
  targetValue: 8
  missionId: "main_task_group-02-01#55"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#55"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 56
  title: P課題56
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#56"
  threshold: 34000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#56"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 57
  title: P課題57
  description: コンテストに挑戦しよう
  homeDescription: コンテストに挑戦しよう
  missionType: MissionType_IncrementPvpRatePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#57"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#57"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 58
  title: P課題58
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#58"
  threshold: 160
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#58"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 59
  title: P課題59
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#59"
  threshold: 30
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#59"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 60
  title: P課題60
  description: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  homeDescription: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#60"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#60"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 500
  additionalRewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 61
  title: P課題61
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#61"
  threshold: 30
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#61"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 62
  title: P課題62
  description: プロデュースに挑戦して評価A以上を獲得しよう
  homeDescription: プロデュースに挑戦して評価A以上を獲得しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 8
  missionId: "main_task_group-02-01#62"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#62"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 63
  title: P課題63
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#63"
  threshold: 15000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#63"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 64
  title: P課題64
  description: サポートカード{threshold}枚をLv40にしよう
  homeDescription: サポートカード{threshold}枚をLv40にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 40
  missionId: "main_task_group-02-01#64"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#64"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 65
  title: P課題65
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#65"
  threshold: 33
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#65"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 66
  title: P課題66
  description: |-
    プロデュースに挑戦して評価A以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価A以上を獲得しよう
    【条件】プラン：センスのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan1
  targetValue: 8
  missionId: "main_task_group-02-01#66"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#66"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 67
  title: P課題67
  description: |-
    プロデュースに挑戦して評価A以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  homeDescription: |-
    プロデュースに挑戦して評価A以上を獲得しよう
    【条件】プラン：ロジックのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanPlayCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan2
  targetValue: 8
  missionId: "main_task_group-02-01#67"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#67"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 68
  title: P課題68
  description: 合計ファン数を{threshold}人にしよう
  homeDescription: 合計ファン数を{threshold}人にしよう
  missionType: MissionType_AbsoluteFanCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#68"
  threshold: 1000000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#68"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 69
  title: P課題69
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#69"
  threshold: 210
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#69"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 70
  title: P課題70
  description: プロデュースでスキルカードを{threshold}回強化しよう
  homeDescription: プロデュースでスキルカードを{threshold}回強化しよう
  missionType: MissionType_IncrementProduceUpgradeProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#70"
  threshold: 30
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#70"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 71
  title: P課題71
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#71"
  threshold: 60
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#71"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 72
  title: P課題72
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#72"
  threshold: 36000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#72"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 73
  title: P課題73
  description: TrueEndアチーブメントを{threshold}個達成
  homeDescription: TrueEndアチーブメントを{threshold}個達成
  missionType: MissionType_AbsoluteProduceCharacterEnding
  targetIds1: []
  targetIds2: []
  targetIds3:
  - TrueEnd
  targetValue: 0
  missionId: "main_task_group-02-01#73"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#73"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 74
  title: P課題74
  description: サポートカード{threshold}枚をLv40にしよう
  homeDescription: サポートカード{threshold}枚をLv40にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 40
  missionId: "main_task_group-02-01#74"
  threshold: 4
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#74"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 75
  title: P課題75
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#75"
  threshold: 35
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#75"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 76
  title: P課題76
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#76"
  threshold: 18000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#76"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 77
  title: P課題77
  description: 篠澤 広で最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 篠澤 広で最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - shro
  targetValue: 8
  missionId: "main_task_group-02-01#77"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#77"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 78
  title: P課題78
  description: 倉本 千奈で最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 倉本 千奈で最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - kcna
  targetValue: 8
  missionId: "main_task_group-02-01#78"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#78"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 79
  title: P課題79
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#79"
  threshold: 230
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#79"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 80
  title: P課題80
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#80"
  threshold: 40000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#80"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1350
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 81
  title: P課題81
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#81"
  threshold: 100
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#81"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 82
  title: P課題82
  description: 葛城 リーリヤで最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 葛城 リーリヤで最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - kllj
  targetValue: 8
  missionId: "main_task_group-02-01#82"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#82"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 83
  title: P課題83
  description: 紫雲 清夏で最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 紫雲 清夏で最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - ssmk
  targetValue: 8
  missionId: "main_task_group-02-01#83"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#83"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 84
  title: P課題84
  description: プロデュースアイドル{threshold}人の特訓段階を5にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を5にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 5
  missionId: "main_task_group-02-01#84"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#84"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 85
  title: P課題85
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#85"
  threshold: 37
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#85"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 86
  title: P課題86
  description: プロデュース評価を合計{threshold}獲得しよう
  homeDescription: プロデュース評価を合計{threshold}獲得しよう
  missionType: MissionType_IncrementProduceTotalScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#86"
  threshold: 50000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#86"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 87
  title: P課題87
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#87"
  threshold: 20000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#87"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 88
  title: P課題88
  description: 有村 麻央で最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 有村 麻央で最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - amao
  targetValue: 8
  missionId: "main_task_group-02-01#88"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#88"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 89
  title: P課題89
  description: 姫崎 莉波で最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 姫崎 莉波で最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - hrnm
  targetValue: 8
  missionId: "main_task_group-02-01#89"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#89"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 90
  title: P課題90
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#90"
  threshold: 250
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#90"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 1500
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 91
  title: P課題91
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#91"
  threshold: 150
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#91"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 92
  title: P課題92
  description: 月村 手毬で最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 月村 手毬で最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - ttmr
  targetValue: 8
  missionId: "main_task_group-02-01#92"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#92"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 93
  title: P課題93
  description: 藤田 ことねで最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 藤田 ことねで最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - fktn
  targetValue: 8
  missionId: "main_task_group-02-01#93"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#93"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 94
  title: P課題94
  description: スキルカードを{threshold}種類獲得しよう
  homeDescription: スキルカードを{threshold}種類獲得しよう
  missionType: MissionType_AbsoluteProducePictureBookProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#94"
  threshold: 200
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#94"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 95
  title: P課題95
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#95"
  threshold: 40
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#95"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 96
  title: P課題96
  description: プロデュースアイドル{threshold}人の特訓段階を6にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を6にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 6
  missionId: "main_task_group-02-01#96"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#96"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 97
  title: P課題97
  description: 花海 咲季で最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 花海 咲季で最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - hski
  targetValue: 8
  missionId: "main_task_group-02-01#97"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#97"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 98
  title: P課題98
  description: 花海 佑芽で最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: 花海 佑芽で最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProduceCharacterClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - hume
  targetValue: 8
  missionId: "main_task_group-02-01#98"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#98"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 99
  title: P課題99
  description: TrueEndアチーブメントを{threshold}個達成
  homeDescription: TrueEndアチーブメントを{threshold}個達成
  missionType: MissionType_AbsoluteProduceCharacterEnding
  targetIds1: []
  targetIds2: []
  targetIds3:
  - TrueEnd
  targetValue: 0
  missionId: "main_task_group-02-01#99"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#99"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-02-01
  number: 100
  title: P課題100
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-02-01#100"
  threshold: 200
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-02-01#100"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 250
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 1
  title: P課題2-1
  description: 研修の【基礎７】全力をクリアしよう
  homeDescription: 研修の【基礎７】全力をクリアしよう
  missionType: MissionType_AbsoluteSeminarExamClear
  targetIds1:
  - seminar_gruop-01_08
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#1"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#1"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 2
  title: P課題2-2
  description: 研修の【基礎８】強気をクリアしよう
  homeDescription: 研修の【基礎８】強気をクリアしよう
  missionType: MissionType_AbsoluteSeminarExamClear
  targetIds1:
  - seminar_gruop-01_07
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#2"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#2"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 3
  title: P課題2-3
  description: プラン：アノマリーのプロデュースアイドルで最終試験に{threshold}回合格しよう
  homeDescription: プラン：アノマリーのプロデュースアイドルで最終試験に{threshold}回合格しよう
  missionType: MissionType_IncrementProducePlanClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan3
  targetValue: 0
  missionId: "main_task_group-03-01#3"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#3"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 4
  title: P課題2-4
  description: アノマリーガシャを{threshold}回引こう
  homeDescription: アノマリーガシャを{threshold}回引こう
  missionType: MissionType_IncrementShopCoinGashaDrawCount
  targetIds1:
  - coin_gasha-c
  - coin_gasha-c_-01
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#4"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#4"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 5
  title: P課題2-5
  description: 研修の【応用５】プラン：アノマリーをクリアしよう
  homeDescription: 研修の【応用５】プラン：アノマリーをクリアしよう
  missionType: MissionType_AbsoluteSeminarExamClear
  targetIds1:
  - seminar_gruop-02_05
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#5"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#5"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 6
  title: P課題2-6
  description: プロデュースアイドル{threshold}人の特訓段階を4にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を4にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 4
  missionId: "main_task_group-03-01#6"
  threshold: 6
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#6"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 7
  title: P課題2-7
  description: 十王 星南の親愛度Lvを{threshold}にしよう
  homeDescription: 十王 星南の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - jsna
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#7"
  threshold: 4
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#7"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 8
  title: P課題2-8
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#8"
  threshold: 220
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#8"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 9
  title: P課題2-9
  description: スキルカードを{threshold}種類獲得しよう
  homeDescription: スキルカードを{threshold}種類獲得しよう
  missionType: MissionType_AbsoluteProducePictureBookProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#9"
  threshold: 220
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#9"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 10
  title: P課題2-10
  description: 十王 星南の親愛度Lvを{threshold}にしよう
  homeDescription: 十王 星南の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - jsna
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#10"
  threshold: 7
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#10"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 11
  title: P課題2-11
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#11"
  threshold: 270
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#11"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 12
  title: P課題2-12
  description: 十王 星南の親愛度Lvを{threshold}にしよう
  homeDescription: 十王 星南の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - jsna
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#12"
  threshold: 9
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#12"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 13
  title: P課題2-13
  description: 敏腕P研修のいずれかをクリアしよう
  homeDescription: 敏腕P研修のいずれかをクリアしよう
  missionType: MissionType_AbsoluteSeminarExamClear
  targetIds1:
  - seminar_gruop-04_01
  - seminar_gruop-04_02
  - seminar_gruop-04_03
  - seminar_gruop-04_04
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#13"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#13"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 14
  title: P課題2-14
  description: プロデュースで定期公演『初』：マスターに{threshold}回挑戦しよう
  homeDescription: プロデュースで定期公演『初』：マスターに{threshold}回挑戦しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2:
  - produce-003
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#14"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#14"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 15
  title: P課題2-15
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#15"
  threshold: 23000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#15"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 16
  title: P課題2-16
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#16"
  threshold: 240
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#16"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 17
  title: P課題2-17
  description: サポートカード{threshold}枚をLv40にしよう
  homeDescription: サポートカード{threshold}枚をLv40にしよう
  missionType: MissionType_AbsoluteSupportCardLevelCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 40
  missionId: "main_task_group-03-01#17"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#17"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 18
  title: P課題2-18
  description: プラン：アノマリーのプロデュースアイドルで最終試験に合格して評価A以上を{threshold}回獲得しよう
  homeDescription: プラン：アノマリーのプロデュースアイドルで最終試験に合格して評価A以上を{threshold}回獲得しよう
  missionType: MissionType_IncrementProducePlanClearCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Plan3
  targetValue: 8
  missionId: "main_task_group-03-01#18"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#18"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 19
  title: P課題2-19
  description: プロデュースアイドル{threshold}人の特訓段階を6にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を6にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 6
  missionId: "main_task_group-03-01#19"
  threshold: 2
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#19"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 20
  title: P課題2-20
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#20"
  threshold: 50000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#20"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 21
  title: P課題2-21
  description: APを{threshold}消費しよう
  homeDescription: APを{threshold}消費しよう
  missionType: MissionType_IncrementConsumeActionPoint
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#21"
  threshold: 100
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#21"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 22
  title: P課題2-22
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#22"
  threshold: 260
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#22"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 23
  title: P課題2-23
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#23"
  threshold: 300
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#23"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 24
  title: P課題2-24
  description: プロデュースでいずれかのパラメータを1500以上に育成しよう
  homeDescription: プロデュースでいずれかのパラメータを1500以上に育成しよう
  missionType: MissionType_ProduceConditionClear
  targetIds1:
  - p_cd-any_vodavi-1500
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#24"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#24"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 25
  title: P課題2-25
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#25"
  threshold: 30000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#25"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 26
  title: P課題2-26
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#26"
  threshold: 43
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#26"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 27
  title: P課題2-27
  description: スキルカードを{threshold}種類獲得しよう
  homeDescription: スキルカードを{threshold}種類獲得しよう
  missionType: MissionType_AbsoluteProducePictureBookProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#27"
  threshold: 250
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#27"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 28
  title: P課題2-28
  description: 合計ファン数を{threshold}人にしよう
  homeDescription: 合計ファン数を{threshold}人にしよう
  missionType: MissionType_AbsoluteFanCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#28"
  threshold: 2000000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#28"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 29
  title: P課題2-29
  description: |-
    定期公演『初』：マスターで最終試験に{threshold}回合格しよう
    【条件】プラン：アノマリーのプロデュースアイドル
  homeDescription: |-
    定期公演『初』：マスターで最終試験に{threshold}回合格しよう
    【条件】プラン：アノマリーのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanClearCount
  targetIds1: []
  targetIds2:
  - produce-003
  targetIds3:
  - Plan3
  targetValue: 0
  missionId: "main_task_group-03-01#29"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#29"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class3
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 30
  title: P課題2-30
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#30"
  threshold: 280
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#30"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 31
  title: P課題2-31
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#31"
  threshold: 60000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#31"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 32
  title: P課題2-32
  description: お仕事を合計{threshold}時間しよう
  homeDescription: お仕事を合計{threshold}時間しよう
  missionType: MissionType_IncrementWorkDurationHour
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#32"
  threshold: 20
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#32"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 33
  title: P課題2-33
  description: プロデュースアイドル{threshold}人の特訓段階を6にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を6にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 6
  missionId: "main_task_group-03-01#33"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#33"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 34
  title: P課題2-34
  description: プロデュースに挑戦して評価A+以上を獲得しよう
  homeDescription: プロデュースに挑戦して評価A+以上を獲得しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 9
  missionId: "main_task_group-03-01#34"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#34"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 35
  title: P課題2-35
  description: 十王 星南のファンを{threshold}人獲得しよう
  homeDescription: 十王 星南のファンを{threshold}人獲得しよう
  missionType: MissionType_AbsoluteFanCount
  targetIds1:
  - jsna
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#35"
  threshold: 100000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#35"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 36
  title: P課題2-36
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#36"
  threshold: 40000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#36"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 37
  title: P課題2-37
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#37"
  threshold: 300
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#37"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 38
  title: P課題2-38
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#38"
  threshold: 45
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#38"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 39
  title: P課題2-39
  description: 花海 咲季の親愛度Lvを{threshold}にしよう
  homeDescription: 花海 咲季の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - hski
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#39"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#39"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 40
  title: P課題2-40
  description: 月村 手毬の親愛度Lvを{threshold}にしよう
  homeDescription: 月村 手毬の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - ttmr
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#40"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#40"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 41
  title: P課題2-41
  description: 藤田 ことねの親愛度Lvを{threshold}にしよう
  homeDescription: 藤田 ことねの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - fktn
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#41"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#41"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 42
  title: P課題2-42
  description: 有村 麻央の親愛度Lvを{threshold}にしよう
  homeDescription: 有村 麻央の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - amao
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#42"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#42"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 43
  title: P課題2-43
  description: 葛城 リーリヤの親愛度Lvを{threshold}にしよう
  homeDescription: 葛城 リーリヤの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - kllj
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#43"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#43"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 44
  title: P課題2-44
  description: 倉本 千奈の親愛度Lvを{threshold}にしよう
  homeDescription: 倉本 千奈の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - kcna
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#44"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#44"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 45
  title: P課題2-45
  description: 紫雲 清夏の親愛度Lvを{threshold}にしよう
  homeDescription: 紫雲 清夏の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - ssmk
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#45"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#45"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 46
  title: P課題2-46
  description: 篠澤 広の親愛度Lvを{threshold}にしよう
  homeDescription: 篠澤 広の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - shro
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#46"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#46"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 47
  title: P課題2-47
  description: 花海 佑芽の親愛度Lvを{threshold}にしよう
  homeDescription: 花海 佑芽の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - hume
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#47"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#47"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 48
  title: P課題2-48
  description: 姫崎 莉波の親愛度Lvを{threshold}にしよう
  homeDescription: 姫崎 莉波の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - hrnm
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#48"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#48"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 49
  title: P課題2-49
  description: 十王 星南の親愛度Lvを{threshold}にしよう
  homeDescription: 十王 星南の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - jsna
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#49"
  threshold: 10
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#49"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 50
  title: P課題2-50
  description: プロデュースに挑戦して評価S以上を獲得しよう
  homeDescription: プロデュースに挑戦して評価S以上を獲得しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 10
  missionId: "main_task_group-03-01#50"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#50"
  rewards:
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 51
  title: P課題51
  description: プロデュースでN.I.Aに挑戦しよう
  homeDescription: プロデュースでN.I.Aに挑戦しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1:
  - produce_group-002
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#51"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#51"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 52
  title: P課題52
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#52"
  threshold: 320
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#52"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 53
  title: P課題53
  description: プロデュースアイドル{threshold}人の特訓段階を6にしよう
  homeDescription: プロデュースアイドル{threshold}人の特訓段階を6にしよう
  missionType: MissionType_AbsoluteIdolCardLevelLimitRankCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 6
  missionId: "main_task_group-03-01#53"
  threshold: 4
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#53"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 54
  title: P課題54
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#54"
  threshold: 70000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#54"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 55
  title: P課題55
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#55"
  threshold: 50000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#55"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 56
  title: P課題56
  description: コンテストに挑戦しよう
  homeDescription: コンテストに挑戦しよう
  missionType: MissionType_IncrementPvpRatePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#56"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#56"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 57
  title: P課題57
  description: N.I.Aでスキルカードを{threshold}回カスタマイズしよう
  homeDescription: N.I.Aでスキルカードを{threshold}回カスタマイズしよう
  missionType: MissionType_IncrementProduceCustomizeProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#57"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#57"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 58
  title: P課題58
  description: N.I.Aでファン投票数を{threshold}獲得しよう
  homeDescription: N.I.Aでファン投票数を{threshold}獲得しよう
  missionType: MissionType_IncrementProduceVoteCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#58"
  threshold: 50000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#58"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 59
  title: P課題59
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#59"
  threshold: 350
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#59"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 60
  title: P課題60
  description: N.I.Aで最終オーディションに合格しよう
  homeDescription: N.I.Aで最終オーディションに合格しよう
  missionType: MissionType_ProduceConditionClear
  targetIds1:
  - p_cd-produce-commu-ending-normal-03
  targetIds2: []
  targetIds3:
  - produce_group-002
  targetValue: 0
  missionId: "main_task_group-03-01#60"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#60"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 61
  title: P課題61
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#61"
  threshold: 320
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#61"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 62
  title: P課題62
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#62"
  threshold: 80000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#62"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 63
  title: P課題63
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#63"
  threshold: 60000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#63"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 64
  title: P課題64
  description: 合計ファン数を{threshold}人にしよう
  homeDescription: 合計ファン数を{threshold}人にしよう
  missionType: MissionType_AbsoluteFanCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#64"
  threshold: 3000000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#64"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 65
  title: P課題65
  description: お仕事を合計{threshold}時間しよう
  homeDescription: お仕事を合計{threshold}時間しよう
  missionType: MissionType_IncrementWorkDurationHour
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#65"
  threshold: 20
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#65"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 66
  title: P課題66
  description: |-
    N.I.Aで最終オーディションに合格して評価S以上達成しよう
    【条件】プラン：センスのプロデュースアイドル
  homeDescription: |-
    N.I.Aで最終オーディションに合格して評価S以上達成しよう
    【条件】プラン：センスのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanClearCount
  targetIds1:
  - produce_group-002
  targetIds2: []
  targetIds3:
  - Plan1
  targetValue: 10
  missionId: "main_task_group-03-01#66"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#66"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 67
  title: P課題67
  description: 最終試験で1位になろう
  homeDescription: 最終試験で1位になろう
  missionType: MissionType_ProduceConditionClear
  targetIds1:
  - p_cd-produce-commu-ending-normal-03
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#67"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#67"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 68
  title: P課題68
  description: スキルカードを{threshold}種類獲得しよう
  homeDescription: スキルカードを{threshold}種類獲得しよう
  missionType: MissionType_AbsoluteProducePictureBookProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#68"
  threshold: 300
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#68"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 69
  title: P課題69
  description: N.I.Aでスキルカードを{threshold}回カスタマイズしよう
  homeDescription: N.I.Aでスキルカードを{threshold}回カスタマイズしよう
  missionType: MissionType_IncrementProduceCustomizeProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#69"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#69"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 70
  title: P課題70
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#70"
  threshold: 370
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#70"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 71
  title: P課題71
  description: プロデュースで特別指導を{threshold}回選択しよう
  homeDescription: プロデュースで特別指導を{threshold}回選択しよう
  missionType: MissionType_IncrementProduceSelectStepCount
  targetIds1: []
  targetIds2: []
  targetIds3:
  - Customize
  targetValue: 0
  missionId: "main_task_group-03-01#71"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#71"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 72
  title: P課題72
  description: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  homeDescription: いずれかのアイドルの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#72"
  threshold: 14
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#72"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 73
  title: P課題73
  description: お仕事を合計{threshold}時間しよう
  homeDescription: お仕事を合計{threshold}時間しよう
  missionType: MissionType_IncrementWorkDurationHour
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#73"
  threshold: 20
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#73"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 74
  title: P課題74
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#74"
  threshold: 48
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#74"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 75
  title: P課題75
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#75"
  threshold: 90000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#75"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 76
  title: P課題76
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#76"
  threshold: 70000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#76"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 77
  title: P課題77
  description: N.I.Aでファン投票数を{threshold}獲得しよう
  homeDescription: N.I.Aでファン投票数を{threshold}獲得しよう
  missionType: MissionType_IncrementProduceVoteCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#77"
  threshold: 100000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#77"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 78
  title: P課題78
  description: |-
    N.I.Aで最終オーディションに合格して評価S以上達成しよう
    【条件】プラン：ロジックのプロデュースアイドル
  homeDescription: |-
    N.I.Aで最終オーディションに合格して評価S以上達成しよう
    【条件】プラン：ロジックのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanClearCount
  targetIds1:
  - produce_group-002
  targetIds2: []
  targetIds3:
  - Plan2
  targetValue: 10
  missionId: "main_task_group-03-01#78"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#78"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 79
  title: P課題79
  description: 合計ファン数を{threshold}人にしよう
  homeDescription: 合計ファン数を{threshold}人にしよう
  missionType: MissionType_AbsoluteFanCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#79"
  threshold: 4000000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#79"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 80
  title: P課題80
  description: アチーブメントを{threshold}個獲得しよう
  homeDescription: アチーブメントを{threshold}個獲得しよう
  missionType: MissionType_AbsoluteAchievementCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#80"
  threshold: 400
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#80"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 81
  title: P課題81
  description: |-
    N.I.Aで最終オーディションに合格して評価S以上達成しよう
    【条件】プラン：アノマリーのプロデュースアイドル
  homeDescription: |-
    N.I.Aで最終オーディションに合格して評価S以上達成しよう
    【条件】プラン：アノマリーのプロデュースアイドル
  missionType: MissionType_IncrementProducePlanClearCount
  targetIds1:
  - produce_group-002
  targetIds2: []
  targetIds3:
  - Plan3
  targetValue: 10
  missionId: "main_task_group-03-01#81"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#81"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 82
  title: P課題82
  description: 花海 咲季の親愛度Lvを{threshold}にしよう
  homeDescription: 花海 咲季の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - hski
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#82"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#82"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 83
  title: P課題83
  description: 月村 手毬の親愛度Lvを{threshold}にしよう
  homeDescription: 月村 手毬の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - ttmr
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#83"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#83"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 84
  title: P課題84
  description: 藤田 ことねの親愛度Lvを{threshold}にしよう
  homeDescription: 藤田 ことねの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - fktn
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#84"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#84"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 85
  title: P課題85
  description: 有村 麻央の親愛度Lvを{threshold}にしよう
  homeDescription: 有村 麻央の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - amao
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#85"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#85"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 86
  title: P課題86
  description: 葛城 リーリヤの親愛度Lvを{threshold}にしよう
  homeDescription: 葛城 リーリヤの親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - kllj
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#86"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#86"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 87
  title: P課題87
  description: 倉本 千奈の親愛度Lvを{threshold}にしよう
  homeDescription: 倉本 千奈の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - kcna
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#87"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#87"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 88
  title: P課題88
  description: 紫雲 清夏の親愛度Lvを{threshold}にしよう
  homeDescription: 紫雲 清夏の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - ssmk
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#88"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#88"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 89
  title: P課題89
  description: 篠澤 広の親愛度Lvを{threshold}にしよう
  homeDescription: 篠澤 広の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - shro
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#89"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#89"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 90
  title: P課題90
  description: 十王 星南の親愛度Lvを{threshold}にしよう
  homeDescription: 十王 星南の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - jsna
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#90"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#90"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 91
  title: P課題91
  description: 花海 佑芽の親愛度Lvを{threshold}にしよう
  homeDescription: 花海 佑芽の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - hume
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#91"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#91"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 92
  title: P課題92
  description: 姫崎 莉波の親愛度Lvを{threshold}にしよう
  homeDescription: 姫崎 莉波の親愛度Lvを{threshold}にしよう
  missionType: MissionType_AbsoluteDearnessLevel
  targetIds1:
  - hrnm
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#92"
  threshold: 17
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#92"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 93
  title: P課題93
  description: N.I.Aでスキルカードを{threshold}回カスタマイズしよう
  homeDescription: N.I.Aでスキルカードを{threshold}回カスタマイズしよう
  missionType: MissionType_IncrementProduceCustomizeProduceCardCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#93"
  threshold: 5
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#93"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 94
  title: P課題94
  description: アイドルへの道で★を合計{threshold}以上獲得しよう
  homeDescription: アイドルへの道で★を合計{threshold}以上獲得しよう
  missionType: MissionType_AbsoluteTowerTotalClearRank
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#94"
  threshold: 340
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#94"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 95
  title: P課題95
  description: "True End : N.I.Aを{threshold}人みよう"
  homeDescription: "True End : N.I.Aを{threshold}人みよう"
  missionType: MissionType_AbsoluteProduceCharacterEnding
  targetIds1:
  - produce_group-002
  targetIds2: []
  targetIds3:
  - TrueEnd
  targetValue: 0
  missionId: "main_task_group-03-01#95"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#95"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 96
  title: P課題96
  description: コンテストのユニット総合力を{threshold}以上にしよう
  homeDescription: コンテストのユニット総合力を{threshold}以上にしよう
  missionType: MissionType_AbsolutePvpRateUnitOverallPower
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#96"
  threshold: 100000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#96"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 97
  title: P課題97
  description: コンテストでスコアを{threshold}以上獲得しよう
  homeDescription: コンテストでスコアを{threshold}以上獲得しよう
  missionType: MissionType_AbsolutePvpRateExamBattleMaxScore
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#97"
  threshold: 80000
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#97"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 98
  title: P課題98
  description: プロデューサーLv{threshold}に到達しよう
  homeDescription: プロデューサーLv{threshold}に到達しよう
  missionType: MissionType_AbsoluteProducerLevel
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 0
  missionId: "main_task_group-03-01#98"
  threshold: 50
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#98"
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 99
  title: P課題99
  description: プロデュースに挑戦して評価S+以上を獲得しよう
  homeDescription: プロデュースに挑戦して評価S+以上を獲得しよう
  missionType: MissionType_IncrementProducePlayCount
  targetIds1: []
  targetIds2: []
  targetIds3: []
  targetValue: 11
  missionId: "main_task_group-03-01#99"
  threshold: 1
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#99"
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards: []
  unlockFeatureTutorialType: TutorialType_Unknown
- mainTaskGroupId: main_task_group-03-01
  number: 100
  title: P課題100
  description: "True End : N.I.Aを{threshold}人みよう"
  homeDescription: "True End : N.I.Aを{threshold}人みよう"
  missionType: MissionType_AbsoluteProduceCharacterEnding
  targetIds1:
  - produce_group-002
  targetIds2: []
  targetIds3:
  - TrueEnd
  targetValue: 0
  missionId: "main_task_group-03-01#100"
  threshold: 3
  viewConditionSetId: ""
  unlockConditionSetId: "main_task_group-03-01#100"
  rewards:
  - resourceType: ResourceType_UserExp
    resourceId: ""
    quantity: 2000
  additionalRewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  unlockFeatureTutorialType: TutorialType_Unknown