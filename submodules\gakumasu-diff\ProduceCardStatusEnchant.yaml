- id: card_enchant-e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1-4-g_effect-lesson_add-3-g_effect-cost_full_power_point_add-1
  produceExamTriggerId: e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-3
  - g_effect-cost_full_power_point_add-1
  triggerCount: 4
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 自身使用後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: の場合、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +3・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostFullPowerPointAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力値コスト値増加
    targetId: Label_CostFullPowerPointAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中4回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1-4-g_effect-lesson_add-5-g_effect-cost_full_power_point_add-1
  produceExamTriggerId: e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-5
  - g_effect-cost_full_power_point_add-1
  triggerCount: 4
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 自身使用後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: の場合、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-full_power_up-p_card_search-target_is_self-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +5・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostFullPowerPointAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力値コスト値増加
    targetId: Label_CostFullPowerPointAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中4回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_card_play_after-p_card_search-active_skill-playing-0_1-g_effect-lesson_add-5
  produceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-active_skill-playing-0_1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-5
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_ActiveSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アクティブスキルカード
    targetId: Label_ActiveSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-active_skill-playing-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 使用後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-active_skill-playing-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+5"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_card_play_after-p_card_search-mental_skill-playing-0_1-g_effect-block_add-5
  produceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-mental_skill-playing-0_1
  produceCardGrowEffectIds:
  - g_effect-block_add-5
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_MentalSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: メンタルスキルカード
    targetId: Label_MentalSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-mental_skill-playing-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 使用後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-mental_skill-playing-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_BlockAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気値増加
    targetId: Label_BlockAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+5"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_card_play_after-p_card_search-playing-effect_group-visible-exam_concentration-000-0_1-2-g_effect-lesson_add-10-g_effect-cost_add-1
  produceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-playing-effect_group-visible-exam_concentration-000-0_1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-10
  - g_effect-cost_add-1
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-playing-effect_group-visible-exam_concentration-000-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード使用後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-playing-effect_group-visible-exam_concentration-000-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +10・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値増加
    targetId: Label_CostAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_card_play_after-p_card_search-playing-effect_group-visible-exam_concentration-000-0_1-2-g_effect-lesson_add-15-g_effect-cost_add-1
  produceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-playing-effect_group-visible-exam_concentration-000-0_1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-15
  - g_effect-cost_add-1
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-playing-effect_group-visible-exam_concentration-000-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード使用後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-playing-effect_group-visible-exam_concentration-000-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +15・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値増加
    targetId: Label_CostAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_card_play_after-p_card_search-target-effect_group-visible-exam_concentration-000-0_1-2-g_effect-lesson_add-10-g_effect-cost_add-1
  produceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-target-effect_group-visible-exam_concentration-000-0_1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-10
  - g_effect-cost_add-1
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-target-effect_group-visible-exam_concentration-000-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード使用後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-target-effect_group-visible-exam_concentration-000-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +10・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値増加
    targetId: Label_CostAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_card_play_after-p_card_search-target-effect_group-visible-exam_concentration-000-0_1-2-g_effect-lesson_add-15-g_effect-cost_add-1
  produceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-target-effect_group-visible-exam_concentration-000-0_1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-15
  - g_effect-cost_add-1
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-target-effect_group-visible-exam_concentration-000-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 効果のスキルカード使用後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_card_play_after-p_card_search-target-effect_group-visible-exam_concentration-000-0_1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +15・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値増加
    targetId: Label_CostAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-1-g_effect-lesson_add-10
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-10
  triggerCount: 1
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +10（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中1回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-1-g_effect-lesson_add-8
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-8
  triggerCount: 1
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +8（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中1回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-1-g_effect-lesson_count_add-1
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_count_add-1
  triggerCount: 1
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonCountAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ上昇回数増加
    targetId: Label_LessonCountAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中1回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-2-g_effect-cost_reduce-1-g_effect-lesson_add-16
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-cost_reduce-1
  - g_effect-lesson_add-16
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostReduce
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値減少
    targetId: Label_CostReduce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: -1・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +16（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-2-g_effect-cost_reduce-1-g_effect-lesson_add-21
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-cost_reduce-1
  - g_effect-lesson_add-21
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostReduce
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値減少
    targetId: Label_CostReduce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: -1・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +21（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-2-g_effect-lesson_add-10
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-10
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +10（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-2-g_effect-lesson_add-15
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-15
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +15（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-2-g_effect-lesson_add-15-g_effect-cost_add-1
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-15
  - g_effect-cost_add-1
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +15・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値増加
    targetId: Label_CostAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-2-g_effect-lesson_add-20
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-20
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +20（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-2-g_effect-lesson_add-25-g_effect-cost_add-1
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-25
  - g_effect-cost_add-1
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +25・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値増加
    targetId: Label_CostAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-2-g_effect-lesson_add-6
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-6
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +6（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-1
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-1
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-2
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-2
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+2"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-3
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-3
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+3"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-4
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-4
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+4"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_concentration-g_effect-lesson_add-5
  produceExamTriggerId: e_trigger-exam_stance_change_concentration
  produceCardGrowEffectIds:
  - g_effect-lesson_add-5
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_concentration
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+5"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-1-2-g_effect-lesson_add-4
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-4
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +4（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-1-2-g_effect-lesson_add-6
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-6
  triggerCount: 2
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +6（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中2回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-1-3-g_effect-lesson_add-10
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-10
  triggerCount: 3
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +10（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中3回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-1-3-g_effect-lesson_add-5
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-5
  triggerCount: 3
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +5（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中3回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-1-4-g_effect-lesson_add-15
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-15
  triggerCount: 4
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +15（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中4回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-1-4-g_effect-lesson_add-20
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-20
  triggerCount: 4
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +20（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中4回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-1-g_effect-lesson_add-2
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-2
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+2"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-1-g_effect-lesson_add-4
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
  produceCardGrowEffectIds:
  - g_effect-lesson_add-4
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-1
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+4"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-2-3-g_effect-lesson_add-4
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
  produceCardGrowEffectIds:
  - g_effect-lesson_add-4
  triggerCount: 3
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を2回変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +4（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中3回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_count_interval-2-3-g_effect-lesson_add-7
  produceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
  produceCardGrowEffectIds:
  - g_effect-lesson_add-7
  triggerCount: 3
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 指針
    targetId: Label_Stance
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: を2回変更するたび、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_count_interval-2
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +7（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中3回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-1-g_effect-full_power_point_add-1-g_effect-cost_add-1
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-full_power_point_add-1
  - g_effect-cost_add-1
  triggerCount: 1
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_FullPowerPointAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力値増加
    targetId: Label_FullPowerPointAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1・
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_CostAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: コスト値増加
    targetId: Label_CostAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中1回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-1-g_effect-lesson_add-20
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-20
  triggerCount: 1
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +20（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中1回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-1-g_effect-lesson_add-30
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-30
  triggerCount: 1
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +30（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中1回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-4-g_effect-lesson_count_add-1
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_count_add-1
  triggerCount: 4
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonCountAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ上昇回数増加
    targetId: Label_LessonCountAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +1（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中4回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-1
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-1
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-2
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-2
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+2"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-3
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-3
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+3"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-4
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-4
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+4"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-5
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-5
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+5"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-6
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-6
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+6"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-7
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-7
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+7"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-8
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-8
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+8"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_add-9
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_add-9
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+9"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_full_power-g_effect-lesson_count_add-1
  produceExamTriggerId: e_trigger-exam_stance_change_full_power
  produceCardGrowEffectIds:
  - g_effect-lesson_count_add-1
  triggerCount: 0
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPower
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力
    targetId: Label_ExamFullPower
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_full_power
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonCountAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ上昇回数増加
    targetId: Label_LessonCountAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "+1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_preservation-1-g_effect-block_add-10
  produceExamTriggerId: e_trigger-exam_stance_change_preservation
  produceCardGrowEffectIds:
  - g_effect-block_add-10
  triggerCount: 1
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_preservation
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_preservation
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_preservation
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_preservation
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_BlockAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気値増加
    targetId: Label_BlockAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +10（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中1回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_stance_change_preservation-1-g_effect-block_add-5
  produceExamTriggerId: e_trigger-exam_stance_change_preservation
  produceCardGrowEffectIds:
  - g_effect-block_add-5
  triggerCount: 1
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_preservation
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_preservation
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_preservation
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: になった時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_stance_change_preservation
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_BlockAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気値増加
    targetId: Label_BlockAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +5（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中1回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_status_change-exam_full_power_point-3-g_effect-lesson_add-11
  produceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
  produceCardGrowEffectIds:
  - g_effect-lesson_add-11
  triggerCount: 3
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPowerPoint
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力値
    targetId: Label_ExamFullPowerPoint
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: が増加後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +11（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中3回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []
- id: card_enchant-e_trigger-exam_status_change-exam_full_power_point-3-g_effect-lesson_add-6
  produceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
  produceCardGrowEffectIds:
  - g_effect-lesson_add-6
  triggerCount: 3
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamAddGrowEffect
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 成長
    targetId: Label_ExamAddGrowEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ：
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 直接効果
    targetId: Label_OnHitEffect
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: で
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPowerPoint
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力値
    targetId: Label_ExamFullPowerPoint
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: が増加後、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: e_trigger-exam_status_change-exam_full_power_point
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: このスキルカードの
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardGrowEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_LessonAdd
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ値増加
    targetId: Label_LessonAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: +6（
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン
    targetId: Convert_002
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 中3回）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: ""
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  effectGroupIds: []