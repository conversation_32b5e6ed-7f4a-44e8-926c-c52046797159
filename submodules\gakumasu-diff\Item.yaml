- id: item-coin-box-gasha-event-001
  name: アザッシー
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-box-story-001-gasha-coin
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1720918799000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: box-gasha-event-001_box_01
  shopCoinGashaId: ""
  storyEventId: box-gasha-event-001
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1719799200000"
  endTime: "1720918799000"
  order: 40
- id: item-coin-box-gasha-event-002
  name: 宵闇ピカピカブレス
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-box-story-002-gasha-coin
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1723687199000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: box-gasha-event-002_box_01
  shopCoinGashaId: ""
  storyEventId: box-gasha-event-002
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1722477600000"
  endTime: "1723687199000"
  order: 40
- id: item-coin-box-gasha-event-003
  name: イタズラをいっぱい
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-box-story-003-gasha-coin
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1728784799000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: box-gasha-event-003_box_01
  shopCoinGashaId: ""
  storyEventId: box-gasha-event-003
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1727661600000"
  endTime: "1728784799000"
  order: 40
- id: item-coin-box-gasha-event-004
  name: ふれふれサンタ
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-box-story-009-gasha-coin
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1734141599000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: box-gasha-event-004_box_01
  shopCoinGashaId: ""
  storyEventId: box-gasha-event-004
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1732759200000"
  endTime: "1734141599000"
  order: 40
- id: item-coin-box-gasha-event-005
  name: はちゃめちゃチョコ
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-box-story-010-gasha-coin
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1739325600000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: box-gasha-event-005_box_01
  shopCoinGashaId: ""
  storyEventId: box-gasha-event-005
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1738375200000"
  endTime: "1739325599000"
  order: 40
- id: item-coin-box-gasha-event-006
  name: 春の訪れ桃かざり
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-box-story-011-gasha-coin
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1741744800000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: box-gasha-event-006_box_01
  shopCoinGashaId: ""
  storyEventId: box-gasha-event-006
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1740708000000"
  endTime: "1741744799000"
  order: 40
- id: item-coin-box-gasha-event-007
  name: はじまりの花かざり
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-story-012-gasha-coin
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1744509599000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: box-gasha-event-007_box_01
  shopCoinGashaId: ""
  storyEventId: box-gasha-event-007
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1743472800000"
  endTime: "1744509599000"
  order: 40
- id: item-coin-box-gasha-event-015
  name: 広島ツアーメダル
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-story-015-gasha-coin
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1749866399000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: box-gasha-event-015_box_01
  shopCoinGashaId: ""
  storyEventId: box-gasha-event-015
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1748484000000"
  endTime: "1749866399000"
  order: 41
- id: item-coin-highscorerush-event-001
  name: ほしのきらめき
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: イベントモードのプロデュースなど
  assetId: item_event-highscorerush
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1740967200000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: highscorerush-event-001_box_01
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: highscorerush-event-001
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1740103200000"
  endTime: "1740686400000"
  order: 40
- id: item-coin-highscorerush-event-002
  name: ほしのきらめき
  description: イベントガシャで使用するアイテム
  acquisitionRouteDescription: イベントモードのプロデュースなど
  assetId: item_event-highscorerush
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1751162400000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: highscorerush-event-002_box_01
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: highscorerush-event-002
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1750298400000"
  endTime: "1750881600000"
  order: 40
- id: item-common-idol_card-piece-r
  name: Rアイドルのピース
  description: Rプロデュースアイドルの才能開花に使用できるアイテム
  acquisitionRouteDescription: 交換所など
  assetId: item_idol-limit-release-r
  type: ItemType_IdolCardPotentialRankUpgrade
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_R
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10001
- id: item-common-idol_card-piece-sr
  name: SRアイドルのピース
  description: SRプロデュースアイドルの才能開花に使用できるアイテム
  acquisitionRouteDescription: 交換所など
  assetId: item_idol-limit-release-sr
  type: ItemType_IdolCardPotentialRankUpgrade
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Sr
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10002
- id: item-common-idol_card-piece-ssr
  name: SSRアイドルのピース
  description: SSRプロデュースアイドルの才能開花に使用できるアイテム
  acquisitionRouteDescription: 交換所など
  assetId: item_idol-limit-release-ssr
  type: ItemType_IdolCardPotentialRankUpgrade
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Ssr
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10003
- id: item-common-support_card-limit-r
  name: 銅の解放券
  description: Rサポートカードの上限解放に使用できるアイテム
  acquisitionRouteDescription: 交換所など
  assetId: item_support-limit-release-r
  type: ItemType_SupportCardLevelLimitRankUpgrade
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_R
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10004
- id: item-common-support_card-limit-sr
  name: 金の解放券
  description: SRサポートカードの上限解放に使用できるアイテム
  acquisitionRouteDescription: 交換所など
  assetId: item_support-limit-release-sr
  type: ItemType_SupportCardLevelLimitRankUpgrade
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Sr
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10005
- id: item-common-support_card-limit-ssr
  name: 虹の解放券
  description: SSRサポートカードの上限解放に使用できるアイテム
  acquisitionRouteDescription: 交換所など
  assetId: item_support-limit-release-ssr
  type: ItemType_SupportCardLevelLimitRankUpgrade
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Ssr
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10006
- id: item-commu-key-001
  name: 記録の鍵
  description: コミュを解放できるアイテム
  acquisitionRouteDescription: マニー交換所など
  assetId: item_commu-key-01
  type: ItemType_StoryUnlockKey
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 40
- id: item-dearness-unlock-ssmk
  name: 清夏との記録の鍵
  description: 紫雲清夏のSTEP3の親愛度コミュを解放するアイテム
  acquisitionRouteDescription: 交換所など
  assetId: item_commu-dearness-key-ssmk
  type: ItemType_DearnessStoryUnlock
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ssmk
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10100
- id: item-friend_coin
  name: フレンドコイン
  description: フレンドガシャで使用するコイン
  acquisitionRouteDescription: レンタル、サークル寄付など
  assetId: item_friend-coin
  type: ItemType_FriendCoin
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: coin_gasha-friend
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1677596400000"
  endTime: "0"
  order: 50
- id: item-gacha-coin-anniversary-001
  name: はつみちゃんコイン
  description: 1st アニバーサリーコインガシャで使用するコイン
  acquisitionRouteDescription: プロデュースなど
  assetId: item_gacha-coin-1st-aniv-001
  type: ItemType_Coin
  rarity: ItemRarity_Unknown
  commonLimitTime: "1748743199000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: coin_gasha-anniversary-001
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1745287200000"
  endTime: "1748743199000"
  order: 60
- id: item-gacha-coin-newyear-001
  name: 初星ふくびき券 2025
  description: 謹賀新年！ふくびきガシャで使用するアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_gacha-coin-newyear-001
  type: ItemType_Coin
  rarity: ItemRarity_Unknown
  commonLimitTime: "1738353599000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: coin_gasha-newyear-001
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1735696800000"
  endTime: "1738353599000"
  order: 101
- id: item-gashaitem-10_01
  name: はつみ印★無料10連ガシャチケット
  description: 最大100連無料！TVCM放映記念10連無料ガシャで使用できるアイテム
  acquisitionRouteDescription: TVCM放映記念ログインボーナス
  assetId: item_gasha-ticket-free-nor-10-2024-01
  type: ItemType_GashaTicket
  rarity: ItemRarity_Sr
  commonLimitTime: "1738421999000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha-00255-free-10-003
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha-00255-free-10-003
    bannerAssetId: img_gasha_banner_gasha-00255-free-10-003-select
    hasFixReward: true
    viewConditionSetId: cd-view-gasha-00255-free-10-003
    unlockConditionSetId: ""
    startTime: "1733104800000"
    endTime: "1738421999000"
  viewConditionSetId: cd-view-gasha-00255-free-10-003
  unlockConditionSetId: ""
  startTime: "1733104800000"
  endTime: "1738421999000"
  order: 50
- id: item-gashaticket-1
  name: プラチナガシャチケット
  description: プラチナガシャで使用するチケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-nor-1
  type: ItemType_GashaTicket
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha-00000-standard-002
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha-00000-standard-001
    bannerAssetId: img_gasha_banner_gasha-00000-standard-001-select
    hasFixReward: false
    viewConditionSetId: ""
    unlockConditionSetId: ""
    startTime: "1711936800000"
    endTime: "1731718799000"
  - id: gasha-00000-standard-002
    bannerAssetId: img_gasha_banner_gasha-00000-standard-001-select
    hasFixReward: false
    viewConditionSetId: ""
    unlockConditionSetId: ""
    startTime: "1731718800000"
    endTime: "0"
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1731718800000"
  endTime: "0"
  order: 30
- id: item-gashaticket-10
  name: プラチナ10連ガシャチケット
  description: プラチナガシャで使用する10連チケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-nor-10
  type: ItemType_GashaTicket
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha-00000-standard-002
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha-00000-standard-001
    bannerAssetId: img_gasha_banner_gasha-00000-standard-001-select
    hasFixReward: true
    viewConditionSetId: ""
    unlockConditionSetId: ""
    startTime: "1711936800000"
    endTime: "1731718799000"
  - id: gasha-00000-standard-002
    bannerAssetId: img_gasha_banner_gasha-00000-standard-001-select
    hasFixReward: true
    viewConditionSetId: ""
    unlockConditionSetId: ""
    startTime: "1731718800000"
    endTime: "0"
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1731718800000"
  endTime: "0"
  order: 50
- id: item-gashaticket-ssr-fix_1
  name: SSR確定ガシャチケット
  description: SSR確定チケットガシャで使用するチケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-ssr-1
  type: ItemType_GashaTicket
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha_ticket_ssr_idol_support_1
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha_ticket_ssr_idol_support_1
    bannerAssetId: img_gasha_banner_gasha_ticket_ssr_idol_support_1-select
    hasFixReward: false
    viewConditionSetId: cd_gasha_have_ticket_ssr-fix_1
    unlockConditionSetId: ""
    startTime: "1709258400000"
    endTime: "0"
  viewConditionSetId: cd_gasha_have_ticket_ssr-fix_1
  unlockConditionSetId: ""
  startTime: "1709258400000"
  endTime: "0"
  order: 31
- id: item-gashaticket-ssr-fix_1_1st-anniversary-001
  name: 1st アニバーサリー記念SSR確定チケット
  description: 1st アニバーサリー記念SSR確定チケットガシャで使用するチケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-ssr-3
  type: ItemType_GashaTicket
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha-ticket-ssr-idol-support-3
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha-ticket-ssr-idol-support-3
    bannerAssetId: img_gasha_banner_gasha-ticket-ssr-idol-support-3-select
    hasFixReward: false
    viewConditionSetId: cd_gasha_have_ticket_ssr-3
    unlockConditionSetId: ""
    startTime: "1745287200000"
    endTime: "0"
  viewConditionSetId: cd_gasha_have_ticket_ssr-3
  unlockConditionSetId: ""
  startTime: "1745287200000"
  endTime: "0"
  order: 34
- id: item-gashaticket-ssr-fix_10-hoten
  name: SSR10連ガシャチケット
  description: 入学記念48時間限定SSR確定10連ガシャで使用するチケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-nor-10
  type: ItemType_GashaTicket
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha_ssr_10
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha_ssr_10
    bannerAssetId: img_gasha_banner_gasha_ssr_10-select
    hasFixReward: true
    viewConditionSetId: cd-gasha_ssr_10-tutorial_clear_48h
    unlockConditionSetId: ""
    startTime: "1709258400000"
    endTime: "0"
  viewConditionSetId: cd-gasha_ssr_10-tutorial_clear_48h
  unlockConditionSetId: ""
  startTime: "1709258400000"
  endTime: "0"
  order: 50
- id: item-gashaticket-ssr-idol-fix_1
  name: SSR Pアイドル確定ガシャチケット
  description: SSR Pアイドル確定チケットガシャで使用するチケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-3-3
  type: ItemType_GashaTicket
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha_ticket_ssr_idol_1
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha_ticket_ssr_idol_1
    bannerAssetId: img_gasha_banner_gasha_ticket_ssr_idol_1-select
    hasFixReward: false
    viewConditionSetId: cd_gasha_have_ticket_ssr-idol-fix_1
    unlockConditionSetId: ""
    startTime: "1709258400000"
    endTime: "0"
  viewConditionSetId: cd_gasha_have_ticket_ssr-idol-fix_1
  unlockConditionSetId: ""
  startTime: "1709258400000"
  endTime: "0"
  order: 32
- id: item-gashaticket-ssr-idol-fix_2
  name: The 1st Period公演記念 SSR Pアイドル確定チケット
  description: The 1st Period公演記念 SSR Pアイドル確定チケットガシャで使用するチケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-ssr-4
  type: ItemType_GashaTicket
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha-ticket-ssr-idol-2-select
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha-ticket-ssr-idol-2-select
    bannerAssetId: img_gasha_banner_gasha-ticket-ssr-idol-2-select
    hasFixReward: false
    viewConditionSetId: cd_gasha_have_item-gashaticket-ssr-idol-fix_2
    unlockConditionSetId: ""
    startTime: "1748088000000"
    endTime: "0"
  viewConditionSetId: cd_gasha_have_item-gashaticket-ssr-idol-fix_2
  unlockConditionSetId: ""
  startTime: "1748088000000"
  endTime: "0"
  order: 40
- id: item-gashaticket-ssr-support-fix_1
  name: SSR Sカード確定ガシャチケット
  description: SSR Sカード確定チケットガシャで使用するチケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-3-2
  type: ItemType_GashaTicket
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha_ticket_ssr_support_1
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha_ticket_ssr_support_1
    bannerAssetId: img_gasha_banner_gasha_ticket_ssr_support_1-select
    hasFixReward: false
    viewConditionSetId: cd_gasha_have_ticket_ssr-support-fix_1
    unlockConditionSetId: ""
    startTime: "1709258400000"
    endTime: "0"
  viewConditionSetId: cd_gasha_have_ticket_ssr-support-fix_1
  unlockConditionSetId: ""
  startTime: "1709258400000"
  endTime: "0"
  order: 33
- id: item-gashaticket-ssr-support-fix-nia-1
  name: N.I.AチャレンジSSR サポートカード確定ガシャチケット
  description: N.I.AチャレンジSSR サポートカード確定チケットガシャで使用するチケット
  acquisitionRouteDescription: ""
  assetId: item_gacha-ticket-3-4
  type: ItemType_GashaTicket
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: gasha-ticket-ssr-support-2
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas:
  - id: gasha-ticket-ssr-support-2
    bannerAssetId: img_gasha_banner_gasha_ticket_ssr_support_2-select
    hasFixReward: false
    viewConditionSetId: cd_gasha_have_ticket-ssr-support-fix-nia-1
    unlockConditionSetId: ""
    startTime: "1749693600000"
    endTime: "0"
  viewConditionSetId: cd_gasha_have_ticket-ssr-support-fix-nia-1
  unlockConditionSetId: ""
  startTime: "1749693600000"
  endTime: "0"
  order: 35
- id: item-Idolcardpiece-exchange-1
  name: フラワー
  description: 交換所で使用するアイテム
  acquisitionRouteDescription: ガシャ、アイドルのピース交換など
  assetId: item_exchange-a
  type: ItemType_ExchangeMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Item
  exchangeId: exchange-piece-1
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 80
- id: item-limitovermaterial-1
  name: レッスンノート
  description: プロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース、課題、アチーブメントなど
  assetId: item_sp-tra-a-com-001
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 1
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1000
- id: item-limitovermaterial-2
  name: ベテランノート
  description: プロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース、お仕事、コインガシャなど
  assetId: item_sp-tra-b-com-001
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 2
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1002
- id: item-limitovermaterial-2-a-1
  name: センスノート（ボーカル）
  description: プラン：センスのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：センスのプロデュースアイドル）など
  assetId: item_sp-tra-b-plana-001
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1004
- id: item-limitovermaterial-2-a-2
  name: センスノート（ダンス）
  description: プラン：センスのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：センスのプロデュースアイドル）など
  assetId: item_sp-tra-b-plana-002
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1005
- id: item-limitovermaterial-2-a-3
  name: センスノート（ビジュアル）
  description: プラン：センスのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：センスのプロデュースアイドル）など
  assetId: item_sp-tra-b-plana-003
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1006
- id: item-limitovermaterial-2-b-1
  name: ロジックノート（ボーカル）
  description: プラン：ロジックのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：ロジックのプロデュースアイドル）など
  assetId: item_sp-tra-b-planb-001
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1007
- id: item-limitovermaterial-2-b-2
  name: ロジックノート（ダンス）
  description: プラン：ロジックのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：ロジックのプロデュースアイドル）など
  assetId: item_sp-tra-b-planb-002
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1008
- id: item-limitovermaterial-2-b-3
  name: ロジックノート（ビジュアル）
  description: プラン：ロジックのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：ロジックのプロデュースアイドル）など
  assetId: item_sp-tra-b-planb-003
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1009
- id: item-limitovermaterial-2-c-1
  name: アノマリーノート（ボーカル）
  description: プラン：アノマリーのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：アノマリーのプロデュースアイドル）など
  assetId: item_sp-tra-b-planc-001
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1010
- id: item-limitovermaterial-2-c-2
  name: アノマリーノート（ダンス）
  description: プラン：アノマリーのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：アノマリーのプロデュースアイドル）など
  assetId: item_sp-tra-b-planc-002
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1011
- id: item-limitovermaterial-2-c-3
  name: アノマリーノート（ビジュアル）
  description: プラン：アノマリーのプロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース（プラン：アノマリーのプロデュースアイドル）など
  assetId: item_sp-tra-b-planc-003
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 4
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1012
- id: item-limitovermaterial-3
  name: マスターノート
  description: プロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: プロデュース、コンテスト交換所、イベントなど
  assetId: item_sp-tra-c-com-001
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1020
- id: item-limitovermaterial-4
  name: レジェンドノート
  description: プロデュースアイドルの特訓で使用するアイテム
  acquisitionRouteDescription: アイテム交換所など
  assetId: item_sp-tra-d-com-001
  type: ItemType_IdolCardLevelLimitMaterial
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1030
- id: item-medal-circle-battle-event-001
  name: 地方行脚メダル
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: 地方行脚イベント
  assetId: item_event-circle-battle-medal
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1731117599000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-circle-battle-event-001
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1730080800000"
  endTime: "1731117599000"
  order: 40
- id: item-medal-event-all-001
  name: 初星トロフィー
  description: 初星トロフィー交換所で使用するアイテム
  acquisitionRouteDescription: イベントなど
  assetId: item_event-exchange-trophy
  type: ItemType_Medal
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Item
  exchangeId: exchange-event-all-001
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1730080800000"
  endTime: "0"
  order: 40
- id: item-medal-main-story-event-001
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースで入手
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1718931599000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-001
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-001
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1717984800000"
  endTime: "1718931599000"
  order: 40
- id: item-medal-main-story-event-002
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1721782799000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-002
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-002
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1720749600000"
  endTime: "1721782799000"
  order: 40
- id: item-medal-main-story-event-003
  name: 初星の栞
  description: 初星コミュイベントで集めるとアイテム交換できます
  acquisitionRouteDescription: プロデュースで入手
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1724464799000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-003
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-003
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1723514400000"
  endTime: "1724464799000"
  order: 40
- id: item-medal-main-story-event-004
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1729821599000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-004
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-004
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1728612000000"
  endTime: "1729821599000"
  order: 40
- id: item-medal-main-story-event-005
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1735091999000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-005
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-005
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1733968800000"
  endTime: "1735091999000"
  order: 40
- id: item-medal-main-story-event-006
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1737770399000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-006
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-006
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1736647200000"
  endTime: "1737770399000"
  order: 40
- id: item-medal-main-story-event-007
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1740103199000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-007
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-007
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1739152800000"
  endTime: "1740103199000"
  order: 40
- id: item-medal-main-story-event-008
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1742695199000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-008
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-008
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1741572000000"
  endTime: "1742695200000"
  order: 40
- id: item-medal-main-story-event-009
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1746928799000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-009
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-009
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1746064800000"
  endTime: "1746928800000"
  order: 40
- id: item-medal-main-story-event-010
  name: 初星の栞
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_main-story-event-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1747533599000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-main-story-event-010
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: main-story-event-010
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1746756000000"
  endTime: "1747533600000"
  order: 40
- id: item-medal-story-event-001
  name: なかよしポッパー
  description: イベント交換所で様々な報酬と交換できる
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-story-001-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1717945199000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-story-event-001
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: story-event-001
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1716343200000"
  endTime: "1717945199000"
  order: 40
- id: item-medal-story-event-002
  name: 木枯ニ散蓋
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-story-002-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1726192799000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-story-event-002
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: story-event-002
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1725156000000"
  endTime: "1726192799000"
  order: 40
- id: item-medal-story-event-003
  name: いっちだんけつの証
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-story-003-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1727143199000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-story-event-003
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: story-event-003
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1726020000000"
  endTime: "1727143199000"
  order: 40
- id: item-medal-story-event-004
  name: 新星のきらめき
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-story-005-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1732931999000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-story-event-004
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: story-event-004
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1731722400000"
  endTime: "1732931999000"
  order: 40
- id: item-medal-story-event-006
  name: つなぐはちまき
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-story-014-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1748656800000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-story-event-006
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: story-event-006
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1747360800000"
  endTime: "1748656799000"
  order: 40
- id: item-medal-story-event-013
  name: 修学旅行のお土産
  description: イベント交換所で様々な報酬と交換できるアイテム
  acquisitionRouteDescription: プロデュースなど
  assetId: item_event-story-013-exchange
  type: ItemType_Medal
  rarity: ItemRarity_Unknown
  commonLimitTime: "1745978400000"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Event
  exchangeId: exchange-story-event-013
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: story-event-013
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "1744336800000"
  endTime: "1745978399000"
  order: 40
- id: item-memory_exchange-class1
  name: センスコイン
  description: センスガシャで使用するコイン
  acquisitionRouteDescription: プロデュース（プラン：センスのプロデュースアイドル）など
  assetId: item_gacha-coin-a
  type: ItemType_Coin
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: coin_gasha-a_02
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: cd_plan_gash_renewal_202506
  unlockConditionSetId: cd_memory-inherit-release_open
  startTime: "1748721600000"
  endTime: "0"
  order: 51
- id: item-memory_exchange-class2
  name: ロジックコイン
  description: ロジックガシャで使用するコイン
  acquisitionRouteDescription: プロデュース（プラン：ロジックのプロデュースアイドル）など
  assetId: item_gacha-coin-b
  type: ItemType_Coin
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: coin_gasha-b_02
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: cd_plan_gash_renewal_202506
  unlockConditionSetId: cd_memory-inherit-release_open
  startTime: "1748721600000"
  endTime: "0"
  order: 52
- id: item-memory_exchange-class3
  name: アノマリーコイン
  description: アノマリーガシャで使用するコイン
  acquisitionRouteDescription: プロデュース（プラン：アノマリーのプロデュースアイドル）など
  assetId: item_gacha-coin-c
  type: ItemType_Coin
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: coin_gasha-c_-01
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: cd_plan_gash_renewal_202506
  unlockConditionSetId: cd_memory-inherit-release_open
  startTime: "1748721600000"
  endTime: "0"
  order: 51
- id: item-memory-inherit
  name: メモリー合成チケット
  description: メモリー合成に使用するチケット
  acquisitionRouteDescription: プランコインガシャなど
  assetId: item_memory-Inherit
  type: ItemType_MemoryInherit
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10007
- id: item-money
  name: マニー
  description: 特訓やマニー交換所で使用するアイテム
  acquisitionRouteDescription: 活動費、お仕事など
  assetId: item_money
  type: ItemType_Money
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Daily
  exchangeId: exchange-money-1
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1
- id: item-piece-idol_amao-1-000
  name: "[学園生活]有村麻央のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3010990
- id: item-piece-idol_amao-1-001
  name: "[初恋]有村麻央のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3010795
- id: item-piece-idol_amao-2-000
  name: "[はじまりはカッコよく]有村麻央のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2010990
- id: item-piece-idol_amao-3-000
  name: "[Fluorite]有村麻央のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1010990
- id: item-piece-idol_amao-3-001
  name: "[キミとセミブルー]有村麻央のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: イベントガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1010920
- id: item-piece-idol_amao-3-002
  name: "[Feel Jewel Dream]有村麻央のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1010820
- id: item-piece-idol_amao-3-007
  name: "[Campus mode!!]有村麻央のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1010720
- id: item-piece-idol_amao-3-009
  name: "[雪解けに]有村麻央のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1010640
- id: item-piece-idol_fktn-1-000
  name: "[学園生活]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3003990
- id: item-piece-idol_fktn-1-001
  name: "[初声]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3003870
- id: item-piece-idol_fktn-2-000
  name: "[カワイイ♡はじめました]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2003990
- id: item-piece-idol_fktn-3-000
  name: "[世界一可愛い私]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1003990
- id: item-piece-idol_fktn-3-001
  name: "[Yellow Big Bang！]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1003960
- id: item-piece-idol_fktn-3-002
  name: "[冠菊]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1003870
- id: item-piece-idol_fktn-3-006
  name: "[White Night! White Wish!]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: イベントガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1003750
- id: item-piece-idol_fktn-3-007
  name: "[Campus mode!!]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1003720
- id: item-piece-idol_fktn-3-011
  name: "[雨上がりのアイリス]藤田ことねのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1003590
- id: item-piece-idol_hmsz-1-000
  name: "[学園生活]秦谷美鈴のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3070580
- id: item-piece-idol_hmsz-1-001
  name: "[初陣]秦谷美鈴のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3070535
- id: item-piece-idol_hmsz-2-000
  name: "[ゆっくり、一歩ずつ]秦谷美鈴のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2070580
- id: item-piece-idol_hmsz-3-000
  name: "[ツキノカメ]秦谷美鈴のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1070580
- id: item-piece-idol_hmsz-3-005
  name: "[Campus mode!!]秦谷美鈴のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1070540
- id: item-piece-idol_hrnm-1-000
  name: "[学園生活]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3090990
- id: item-piece-idol_hrnm-1-001
  name: "[初心]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3090840
- id: item-piece-idol_hrnm-2-000
  name: "[『私らしさ』のはじまり]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2090990
- id: item-piece-idol_hrnm-3-000
  name: "[clumsy trick]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1090990
- id: item-piece-idol_hrnm-3-001
  name: "[キミとセミブルー]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1090910
- id: item-piece-idol_hrnm-3-002
  name: "[L.U.V]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1090790
- id: item-piece-idol_hrnm-3-004
  name: "[ようこそ初星温泉]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1090840
- id: item-piece-idol_hrnm-3-007
  name: "[Campus mode!!]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1090720
- id: item-piece-idol_hrnm-3-008
  name: "[ハッピーミルフィーユ]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1090680
- id: item-piece-idol_hrnm-3-012
  name: "[Howling over the World]姫崎莉波のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: イベントガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1090570
- id: item-piece-idol_hski-1-000
  name: "[学園生活]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3001990
- id: item-piece-idol_hski-1-001
  name: "[初声]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3001870
- id: item-piece-idol_hski-2-000
  name: "[わたしが一番！]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2001990
- id: item-piece-idol_hski-3-000
  name: "[Fighting My Way]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1001990
- id: item-piece-idol_hski-3-001
  name: "[Boom Boom Pow]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1001940
- id: item-piece-idol_hski-3-002
  name: "[冠菊]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: イベントガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1001880
- id: item-piece-idol_hski-3-006
  name: "[古今東西ちょちょいのちょい]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1001780
- id: item-piece-idol_hski-3-008
  name: "[Campus mode!!]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1001720
- id: item-piece-idol_hski-3-011
  name: "[桜フォトグラフ]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1001610
- id: item-piece-idol_hski-3-012
  name: "[雨上がりのアイリス]花海咲季のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1001590
- id: item-piece-idol_hume-1-000
  name: "[学園生活]花海佑芽のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3080970
- id: item-piece-idol_hume-1-001
  name: "[初陣]花海佑芽のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3080535
- id: item-piece-idol_hume-2-000
  name: "[アイドル、はじめっ！]花海佑芽のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2080970
- id: item-piece-idol_hume-3-000
  name: "[The Rolling Riceball]花海佑芽のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1080970
- id: item-piece-idol_hume-3-005
  name: "[White Night! White Wish!]花海佑芽のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1080750
- id: item-piece-idol_hume-3-006
  name: "[Campus mode!!]花海佑芽のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1080710
- id: item-piece-idol_jsna-1-000
  name: "[学園生活]十王星南のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3060760
- id: item-piece-idol_jsna-1-001
  name: "[初陣]十王星南のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3060535
- id: item-piece-idol_jsna-2-000
  name: "[一番星]十王星南のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2060760
- id: item-piece-idol_jsna-3-000
  name: "[小さな野望]十王星南のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1060760
- id: item-piece-idol_jsna-3-002
  name: "[Campus mode!!]十王星南のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1060720
- id: item-piece-idol_jsna-3-003
  name: "[ハッピーミルフィーユ]十王星南のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1060670
- id: item-piece-idol_jsna-3-006
  name: "[Our Chant]十王星南のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1060600
- id: item-piece-idol_kcna-1-000
  name: "[学園生活]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3030990
- id: item-piece-idol_kcna-1-001
  name: "[初心]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3030840
- id: item-piece-idol_kcna-2-000
  name: "[胸を張って一歩ずつ]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2030990
- id: item-piece-idol_kcna-3-000
  name: "[Wonder Scale]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1030990
- id: item-piece-idol_kcna-3-001
  name: "[日々、発見的ステップ！]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1030860
- id: item-piece-idol_kcna-3-002
  name: "[仮装狂騒曲]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: イベントガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1030810
- id: item-piece-idol_kcna-3-005
  name: "[ようこそ初星温泉]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1030830
- id: item-piece-idol_kcna-3-007
  name: "[Campus mode!!]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1030710
- id: item-piece-idol_kcna-3-009
  name: "[雪解けに]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1030650
- id: item-piece-idol_kcna-3-012
  name: "[Howling over the World]倉本千奈のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1030570
- id: item-piece-idol_kllj-1-000
  name: "[学園生活]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3020990
- id: item-piece-idol_kllj-1-001
  name: "[初心]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3020840
- id: item-piece-idol_kllj-2-000
  name: "[一つ踏み出した先に]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2020990
- id: item-piece-idol_kllj-3-000
  name: "[白線]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1020990
- id: item-piece-idol_kllj-3-001
  name: "[冠菊]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1020880
- id: item-piece-idol_kllj-3-005
  name: "[White Night! White Wish!]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1020740
- id: item-piece-idol_kllj-3-006
  name: "[Campus mode!!]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1020700
- id: item-piece-idol_kllj-3-008
  name: "[極光]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1020630
- id: item-piece-idol_kllj-3-010
  name: "[桜フォトグラフ]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: イベントガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1020620
- id: item-piece-idol_kllj-3-012
  name: "[Howling over the World]葛城リーリヤのピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1020560
- id: item-piece-idol_shro-1-000
  name: "[学園生活]篠澤広のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3050990
- id: item-piece-idol_shro-1-001
  name: "[初恋]篠澤広のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3050795
- id: item-piece-idol_shro-2-000
  name: "[一番向いてないこと]篠澤広のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2050990
- id: item-piece-idol_shro-3-000
  name: "[光景]篠澤広のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1050990
- id: item-piece-idol_shro-3-001
  name: "[コントラスト]篠澤広のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1050890
- id: item-piece-idol_shro-3-002
  name: "[仮装狂騒曲]篠澤広のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1050800
- id: item-piece-idol_shro-3-007
  name: "[Campus mode!!]篠澤広のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1050710
- id: item-piece-idol_shro-3-008
  name: "[ハッピーミルフィーユ]篠澤広のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: イベントガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1050680
- id: item-piece-idol_ssmk-1-000
  name: "[学園生活]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3040990
- id: item-piece-idol_ssmk-1-001
  name: "[初恋]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3040795
- id: item-piece-idol_ssmk-2-000
  name: "[夢へのリスタート]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2040990
- id: item-piece-idol_ssmk-3-000
  name: "[Tame-Lie-One-Step]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1040990
- id: item-piece-idol_ssmk-3-001
  name: "[キミとセミブルー]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1040920
- id: item-piece-idol_ssmk-3-002
  name: "[カクシタワタシ]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1040730
- id: item-piece-idol_ssmk-3-007
  name: "[Campus mode!!]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1040700
- id: item-piece-idol_ssmk-3-010
  name: "[桜フォトグラフ]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1040620
- id: item-piece-idol_ssmk-3-012
  name: "[Love & Joy]紫雲清夏のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1040550
- id: item-piece-idol_ttmr-1-000
  name: "[学園生活]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3002990
- id: item-piece-idol_ttmr-1-001
  name: "[初声]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3002870
- id: item-piece-idol_ttmr-2-000
  name: "[一匹狼]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 2002990
- id: item-piece-idol_ttmr-3-000
  name: "[Luna say maybe]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1002990
- id: item-piece-idol_ttmr-3-001
  name: "[アイヴイ]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1002980
- id: item-piece-idol_ttmr-3-002
  name: "[仮装狂騒曲]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1002810
- id: item-piece-idol_ttmr-3-007
  name: "[Campus mode!!]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1002720
- id: item-piece-idol_ttmr-3-009
  name: "[雪解けに]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: イベントガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1002650
- id: item-piece-idol_ttmr-3-011
  name: "[雨上がりのアイリス]月村手毬のピース"
  description: プロデュースアイドルの才能開花や解放に使用するアイテム
  acquisitionRouteDescription: ガシャなど
  assetId: ""
  type: ItemType_IdolCardPiece
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1002590
- id: item-produce_continue-1
  name: 再挑戦チケット
  description: プロデュースの再挑戦時に使用するチケット
  acquisitionRouteDescription: AP交換所など
  assetId: item_continue-01
  type: ItemType_ProduceContinue
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 9
- id: item-produce-reroll-memory-001
  name: メモリー再生成チケット
  description: プロデュースのメモリー再生成時に使用するチケット
  acquisitionRouteDescription: AP交換所など
  assetId: item_memory-reroll-plus
  type: ItemType_ProduceRerollMemory
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10
- id: item-produceboostreward_limitovermaterial-1
  name: 入手ノート数アップ
  description: プロデュース開始時に使用すると特訓アイテムの入手量が200%上昇する獲得量上昇アイテム
  acquisitionRouteDescription: AP交換所など
  assetId: item_p-assist-item_02
  type: ItemType_ProduceBoostRewardIdolCardLevelLimitMaterial
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 2000
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1100
- id: item-produceboostreward_support_card_enhance_point-1
  name: 入手サポート強化Ptアップ
  description: プロデュース開始時に使用するとサポート強化Ptの入手量が200%上昇する獲得量上昇アイテム
  acquisitionRouteDescription: AP交換所など
  assetId: item_p-assist-item_01
  type: ItemType_ProduceBoostRewardSupportCardEnhancePoint
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 2000
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 1102
- id: item-pvp_rate_coin-1
  name: コンテストメダル
  description: 交換所で使用するメダル
  acquisitionRouteDescription: コンテスト
  assetId: item_contest-medal
  type: ItemType_PvpRateCoin
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Item
  exchangeId: exchange-contest-1
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: cd-task_clear-01-025
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 100
- id: item-selection-ticket-001
  name: 半周年セレクションチケット
  description: チケット交換所でプロデュースアイドルやサポートカードを交換できるチケット
  acquisitionRouteDescription: ショップなど
  assetId: item_selection-ticket-001
  type: ItemType_ExchangeTicket
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10
- id: item-selection-ticket-002
  name: 1st アニバーサリーセレクションチケット
  description: チケット交換所でプロデュースアイドルやサポートカードを交換できるチケット
  acquisitionRouteDescription: ショップなど
  assetId: item_selection-ticket-002
  type: ItemType_ExchangeTicket
  rarity: ItemRarity_Ssr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: false
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 10
- id: item-staminaregen-1
  name: APドリンク
  description: APが20回復するドリンク
  acquisitionRouteDescription: ミッション、プロデューサーLvアップ報酬など
  assetId: item_ap-add-001
  type: ItemType_ActionPointRecovery
  rarity: ItemRarity_R
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 20
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 15
- id: item-support_card_enhance_point
  name: サポート強化Pt
  description: サポートカードの強化に使用するポイント
  acquisitionRouteDescription: プロデュース、お仕事、課題、アチーブメントなど
  assetId: item_support-pt
  type: ItemType_SupportCardEnhancePoint
  rarity: ItemRarity_Unknown
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Unknown
  exchangeId: ""
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 3
- id: item-support_card-exchange-1
  name: サポートの証
  description: 交換所で使用するアイテム
  acquisitionRouteDescription: サポート変換など
  assetId: item_exchange-b
  type: ItemType_ExchangeMaterial
  rarity: ItemRarity_Sr
  commonLimitTime: "0"
  personalLimitDay: 0
  sellPrice: 0
  effectValue: 0
  viewWithoutPossession: true
  exchangeType: ExchangeType_Item
  exchangeId: exchange-support-piece-1
  gashaId: ""
  coinGashaId: ""
  shopCoinGashaId: ""
  storyEventId: ""
  produceHighScoreEventId: ""
  idolCardRarity: IdolCardRarity_Unknown
  supportCardRarity: SupportCardRarity_Unknown
  characterId: ""
  gashas: []
  viewConditionSetId: ""
  unlockConditionSetId: ""
  startTime: "0"
  endTime: "0"
  order: 8