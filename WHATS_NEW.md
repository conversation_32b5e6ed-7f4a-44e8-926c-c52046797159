# 更新日志
## kaa
### v2025.7.13.0
脚本：
* [新增] 上传报告时一并打包配置文件（#a8a5566）

界面：
* [修复] 修复某些情况下热重载配置失败的问题（#4bea422）

启动器：
* [修复] 修复当文件夹路径存在空格时启动器无法正确启动 kaa 的问题（#63f792d）
* [新增] 启动器 EXE 新增多分辨率图标（#5db3ed6）

其他：
* [其他] 增加对 Python 信息与分辨率信息的日志输出（#5cc9f45）

### v2025.7.9.0
脚本：
* [新增] 配置中支持储存多个培育方案并支持来回切换（#41e7c8b）
* [重构] 将配置数据中的常量移动到单独一个文件中（#4e4b91d）
* [重构] 配置迁移代码移动到单独的模块（#c7d5cd8）
* [重构] 将配置文件类从 kotonebot.kaa.common 中移动到专门的模块 kotonebot.kaa.config（#e0549c6）
* [修复] 尝试修复周数 OCR 失败问题（#e548518）
* [修复] 修复某些情况下培育会卡在初始饮料技能卡二选一上（#0651d94）
* [修复] 修复部分日志缺失的问题（#497561c）

* [重构] 配置迁移代码移动到单独的模块（#c7d5cd8）
* [重构] 将配置文件类从 kotonebot.kaa.common 中移动到专门的模块 kotonebot.kaa.config（#e0549c6）
* [修复] 尝试修复周数 OCR 失败问题（#e548518）
* [修复] 修复某些情况下培育会卡在初始饮料技能卡二选一上（#0651d94）
* [修复] 修复部分日志缺失的问题（#497561c）

* [修复] 修复部分日志缺失的问题（#497561c）


界面：
* [新增] 为新的培育方案增加 UI（#68b0cbd）
* [新增] 新增快速功能启停（#c3d2401）
* [修复] 修复修改设置后需要重启才能生效的问题（#6dd2b35）

框架：
* [新增] ContextOcr 类支持设置 OCR 语言（#a0d3c31）

其他：
* [单测] 为新的培育方案编写单元测试（#ca83fec）
* [其他] v5 到 v6 配置迁移脚本（#ef725b4）

### v2025.7.7.0
脚本：
* [修复] 修复 AP 商店购买时点击坐标偏移问题（#b8b56bb）
* [修复] 修复商店购买时无法识别部分偶像碎片的问题（#353fa3f）
* [修复] 修复 DMM 版购买时无法进入 AP 商店的问题（#f5a4e50）
* [修复] 修复商店购买有几率卡在确认购买对话框上（#cf1605d）
* [修复] 修复某些情况下无法自动关闭领取活动费后的弹窗（#3b3aac6）
* [新增] 金币商店可选使用每日刷新次数（#68dbc48）

界面：
* [新增] UI 支持向局域网开放（#9b37bcf）

框架：
* [新增] 新增目标截图间隔功能（#c8fbf80）

其他：
* [其他] 删除一些无用 Sprite 资源（#03aa2b5）

### v2025.7.5.0
脚本：
* [修复] 修复有几率无法识别到进行中培育的问题（#50d1403）
* [修复] 修复分辨率缩放导致无法识别到菜单按钮（#f2eadad）
* [修复] 修复分辨率缩放时无法检测到工作完成状态的问题（#d9077e7）

框架：
* [重构] 移除 Device.pinned 方法（#5306f5c）

### v2025.7.3.0
启动器：
* [新增] 新启动器现在支持安装指定版本与指定补丁（#456019b）
* [新增] 自动更新可禁用（#3f88c3a）
* [新增] 启动器 C++ EXE 跳板程序（#b377b84）
* [新增] 新启动器（#c4b93f4）

其他：
* [其他] Git 记录提取工具支持 bootstrap scope（#66ea531）

### v2025.6.28.0
脚本：
* [修复] 修正 debug_entry 脚本路径处理逻辑（#b53a055）
* [修复] 修复清理日志功能失效的问题（#ad5c7b7）
* [修复] 修复 debug_entry 执行的脚本没有正确输出日志的问题（#3ceae4c）

界面：
* [新增] 优化日志导出功能（#9574d20）
* [新增] 为 UI 增加部分配置有效性验证（#08a7e71）
* [新增] 加入调试模式警告提示（#a01c37d）
* [新增] 画面 Tab 刷新机制由自动改为手动（#2469fc0）
* [新增] MuMu12 保活模式可选开启（#71170e5）

框架：
* [修复] 修复 Device 中缩放与截图 Hook 的处理顺序不正确问题
* [修复] 修复 NemuIpc 在多显示器下的坐标系问题（#87da282）
* [修复] 修复当目标分辨率与实际分辨率旋转不同时截图会强制拉伸的问题（#e62e65d）
* [修复] 缩放处理支持自动识别旋转（#677932a）
* [修复] 修复 NemuIpc 截图无法响应屏幕旋转导致的分辨率变化（#105a894）
* [新增] NemuIpcImpl 获取显示器 ID 支持自动重试（#0c94acf）
* [新增] 支持 MuMu12 后台保活模式（#e232826）
* [新增] 引入 Nemu 截图与控制方式（#f0b9181）
* [新增] 移除 WindowsImpl 中的分辨率缩放（#415a8df）
* [新增] 支持等比例分辨率缩放（#bb7f603）
* [重构] 组装 Device 改用 recipe 方案（#f01e022）
* [重构] 将传递的 MuMu 路径从 shell 目录改为 MuMu 根目录（#784b8ed）

其他：
* [其他] HsvRangeTool 支持从剪贴板粘贴图片（#1397587）
* [其他] VSCode 新增不初始化 context 的启动配置（#0718678）
* [其他] 调整部分日志输出格式（#bcd8cf2）

### v2025.6.23.0
脚本：
* [新增] 优化了培育开始的逻辑，修复若干 bug（#86313ec）

框架：
* [修复] 修复由于分离 AndroidDevice 方法导致的 typing 问题（#4d76e1a）
* [重构] 将 Commandable 分离为 WindowsCommandable 与 AndroidCommandable（#16a267d）
* [重构] 将启动 remote_server 的逻辑移动到 kaa cli 中（#b8b5ba8）
* [重构] 提取三个 adb-based 截图方法的工厂函数的共同部分（#bd57dc4）
* [重构] 将创建设备的逻辑从 init_context 中移除（#f2599e6）
* [重构] 重构 Device 与 Impl 的创建方式（#2fc9ad5）

其他：
* [其他] 更新上游 submodules（#810c341）

### v2025.6.8.0
脚本：
* [新增] 新增支持自动禁用与恢复 Gakumasu Localify 汉化插件（#264dac2）
* [修复] 修复外出事件中可用按钮数量小于 2 导致的下标越界问题（#2452c8c）
* [修复] 尝试修复第一次领取饮料有几率卡住的问题（#7f2e588）

框架：
* [新增] 新增全局暂停脚本执行功能（#a8e1f0d）

其他：
* [其他] 新增开源协议与免责声明（#733aa6a）

### v2025.5.23.3
脚本：
* [修复] 尝试修复行动页有时进入行动失效的问题（#5901771）

界面：
* [修复] 修复 UI 中模拟器 Tab 初值不正确的问题（#5f5d99c）

其他：
* [其他] 更新上游 submodule（#cd6e128）

### v2025.5.23.2
脚本：
* [修复] 将配置项 adb_emulator_name 默认值置为 None（#f3ef360）
* [修复] 修复即使未勾选启动模拟器也会尝试启动的问题（#0641863）

界面：
* [修复] 修复截图方法 Dropdown 选项问题（#08a6b6f）
* [新增] 修改 MuMu 模拟器检测失败提示（#3358eb6）

### v2025.5.23.1
界面：
* [新增] 当检测模拟器扫描失败时避免整个程序崩溃（#3d47d3a）

框架：
* [修复] 修复 Mumu12Host 意外搜索了国际版 MuMu 导致异常的问题（#191d212）
* [修复] 修复当只存在单个多开实例时 MuMu12Host 报错（#cfd1f96）

### v2025.5.23.0
脚本：
* [修复] 修复 OCR 错误导致无法开始 MASTER 培育（#b4b14fa）
* [修复] 修复标题包含“gakumas”的窗口会被误认为游戏窗口（#ac3be6a）
* [修复] 修改“技能卡自选强化”的选择技能卡逻辑（#a7b9dac）
* [新增] 为 DMM 控制适配 HostProtocol（#68bf47d）
* [重构] 重构竞赛（#dbe9cae）
* [重构] 使用基础类中的矩形与点重构任务（#b434278）

界面：
* [新增] 新增命令行参数 --kill-dmm 与 --kill-game（#397c45b）
* [重构] 重构 save_config() 函数，移除冗长参数（#9a34749）

框架：
* [新增] 整合雷电模拟器与 MuMu 模拟器控制到 kaa 中（#548ba04）
* [新增] 新增 Mumu12Host & LeidianHost 模拟器控制（#6b5f972）
* [新增] 新增一些基础数据类（#2999367）

### v2025.5.16.1
修复了上一个版本打包时缺失了部分文件的问题。

### v2025.5.16.0
脚本：
* [重构] 移除配置文件路径的硬编码（#6fa9250）
* [重构] 重新组织 kotonebot.kaa 模块（#2f0804f）
* [重构] 重命名 kotonebot.tasks 为 kotonebot.kaa（#333b3f0）
* [修复] 修复配置中行动优先级默认值缺少咨询（#0a6d88e）
* [修复] 修复推荐卡检测超时后 fallback 无法正常执行（#c211964）
* [修复] 修复培育开始卡在选择支援卡上（#8dc76e0）

界面：
* [新增] 命令行接口支持指定日志文件位置（#1d177be）

框架：
* [重构] OCR 引擎推迟到启动脚本时加载（#c9c67e6）

其他：
* [其他] 更新上游 submodule
* [其他] 修复构建脚本中 game.db 路径不正确的问题（#80cb528）
* [文档] 更新 README 路线图（#a967732）

### v2025.5.2.2
其他：
* [其他] 更新上游 submodule

### v2025.5.2.1
脚本：
* [修复] 修复 SP 课程点击位置不正确的问题（#54e9bfc）
* [修复] 再次尝试修复培育中课程有时误提前结束（#236bb34）

### v2025.5.2.0
脚本：
* [修复] 尝试修复培育中课程有时误提前结束（#96680a1）
* [修复] 尝试修复点击 SP 课程有几率失效的问题（#2845e37）
* [新增] 培育中选择技能卡时优先选择游戏推荐的卡（#2727f08）

界面：
* [修复] 修复打开代理时 Gradio 有几率启动失败（#1ecf8c4）

框架：
* [新增] 启动模拟器支持指定命令行参数（#a1f34e5）

其他：
* [其他] 更新上游 submodule（#c641868）
* [其他] 修复 justfile windows-shell 不正确的问题（#146d737）
* [其他] 修复 justfile 在 Linux 上无法执行（#01b8583）

### v2025.4.26.0
脚本：
* [新增] 培育中支持处理咨询（#1c8621f）
* [新增] 支持 master 培育（#e0298c8）
* [新增] 新增所有任务执行完毕后关机等（#c6a982b）
* [修复] 修复配置文件中的默认游戏包名不正确的问题（#03fd51b）

界面：
* [新增] 执行单个任务页面支持停止任务（#ed1d122）

框架：
* [新增] 新增远程 Windows 截图控制方式（#15003b1）
* [新增] KotoneBot 类新增 start 方法（#92e4289）

其他：
* [其他] 构建资源时若无法识别 IDE 类型不再报错（#1c4de83）
* [其他] justfile 中新增 resource & devtool 命令（#7d88734）

### v2025.4.17.2
脚本：
* [修复] 修复培育结束会卡在获得硬币对话框上（#f16d6f5）

### v2025.4.17.1
脚本：
* [修复] 修复无法处理是否展示技能卡选择指导对话框（#ace9237）
* [修复] 修复无法处理回忆编成未编满时的提示（#075554d）

### v2025.4.17.0
脚本：
* [新增] 培育中跳过交流可关闭（#88eb618）
* [新增] DMM 版若游戏已启动则不重复启动（#8d7a70a）
* [新增] DMM 版启动前检查管理员权限（#a301160）
* [新增] 支持培育中四五张卡片情况的检测（#0ddcb42）
* [新增] 将授業的 fallback 选项改为第二个选项（#e63e0de）
* [新增] 培育课程/考试中支持处理技能卡移动事件（#07b9dd4）
* [修复] 修复推荐卡检测超时时若剩余手牌为零会报错的问题（#13f5547）
* [修复] 修复培育移卡处理时的下标越界问题（#60316c6）
* [修复] 修复 OCR 识别失败导致培育开始卡在 STEP 检测（#85bc509）
* [修复] 尝试修复跳过交流时会误触饮料的问题（#649d446）
* [修复] 修复无法检测培育开始时二选一选项的选中状态（#60dce1b）
* [修复] 修复无法处理快进未读交流提示框（#4b901d6）
* [修复] 修复睡意卡的字母 T 有时会出现误识别（#e3073c7）
* [修复] 修复领取技能卡在未找到技能卡时下标越界（#f2bc0bc）
* [修复] 修复了kuyo延迟弹出广告，导致识别错误的问题（#b12cf91）

其他：
* [其他] 同步上游 submodule（#d9dfc36）

### v2025.4.12.1
脚本：
* [修复] 尝试修复在某些设备上培育中点击 (0, 0) 无法跳过动画（#ad29398）
* [新增] 为培育再开的周数检测 OCR 加上重试（#1edb223）

### v2025.4.12.0
脚本：
* [修复] 修复领取 P 饮料有时会卡在等待受け取る按钮上（#bfb0360）
* [修复] 修复快速截图下处理更新提示时报错（#125ef82）
* [修复] 修复培育中有时会卡在技能卡自选强化（#4c37989）
* [修复] 修复无法处理培育开始时 Another Ver 弹窗（#d5ff1f4）
* [修复] 修复无法处理可调整默认商店购买数量为 MAX 弹窗（#64aba99）
* [修复] 再次修复无法处理未读交流跳过确认对话框（#82064f7）
* [修复] 修复每周三启动游戏时卡住的问题（#9676934）
* [修复] 尝试修复快速截图下无法处理网络连接错误弹窗（#a18d93d）
* [修复] 修复交流 SKIP 按钮检测在 DMM 540x960 下有时失效的问题（#ff4ed66）
* [新增] 增加命令行调用方式（#9eb1560）
* [新增] 封装 game_ui.dialog 模块用于处理对话框（#0401118）
* [新增] 移除再开培育中对考试开始场景的检测（#c88ec6e）
* [新增] 优化 P 饮料领取（#b1a3b98）
* [新增] 优化推荐卡检测算法（#a768f01）
* [新增] 优化培育中行动页推荐行动检测（#9b8ef74）
* [新增] 优化 acquisitions() 函数执行速度（#4803a73）
* [新增] 移除培育练习结束时的结束动画检测（#3001416）

框架：
* [新增] 为 Task 类新增 id 字段（#2f68f40）

其他：
* [其他] 同步上游 submodule（#a6ba54f）
* [其他] 调整 git submodule 更新命令（#f00960c）
* [其他] make_resources.py 脚本加入对 PyCharm 的支持（#9c6e980）
* [其他] 调整构建脚本，避免每次都重复提取游戏资源（#bf2aa4d）
* [其他] 移除 justfile 中的创建 venv 部分（#d4c2fe5）
* [其他] 移除未使用的 ui\p_idols 图片文件（#8f2fb70）
* [文档] 更新 DEVELOPMENT.md（#6ea16b5）

### v2025.4.6.1
框架：
* [修复] 修复 ImageDb 模块无法读取中文路径图片的问题（#7cfb906）

### v2025.4.6.0
脚本：
* [修复] 修复培育中卡在饮料领取（#3b89f34）
* [修复] 尝试修复快速截图下无法识别跳过未读交流提示（#e44a17d）
* [修复] 修复快速截图下，培育中无法正确获取推荐行动（#cd83e5a）
* [修复] 修复了系统占用过高时，点击'跳过比赛按钮'失败的问题（#93f4d60）
* [新增] 培育开始时若 AP 不足自动使用饮料（#3585d5f）
* [新增] 优化再开中断培育（#c56f9c1）
* [新增] 升级配置文件到 v3（#2ba3eaf）
* [新增] 重构并优化培育偶像选择（#b00db58）
* [新增] 新增图像数据库 image_db 模块（#3b2d60d）
* [新增] ScrollableIterator 类支持自定义起始和结束位置，增加跳过第一次滚动的选项（#f895716）
* [重构] 调整了 commu 模块中的日志输出（#6b5ba4b）

界面：
* [新增] 将 Gradio 监听地址暂时改回 127.0.0.1（#180e49e）

框架：
* [修复] 修复 double_click 多个重载默认间隔时间不一致的问题（#9592816）
* [新增] MockDevice 类支持载入 MatLike 图像（#6d0686c）

其他：
* [其他] 调整部分构建脚本（#47f82f3）
* [其他] 移除 experiments 文件夹（#68490f1）
* [其他] 新增 HsvRangeTool 用于可视化提取颜色范围（#5d5392e）
* [其他] 新增两个脚本用于从上游项目提取所需游戏资源文件（#d85b624）
* [其他] 修改 VSCode launch.json（#23242af）

### v2025.3.30.0
脚本：
* [修复] 修复 in_purodyuusu 模块中的导入问题（#6cd2c97）
* [修复] 修复有时会卡在未读交流上（#ea7ddcc）
* [修复] 修复 PRO 培育开始时会卡在技能卡/饮料二选一的选项上（#4460ee3）
* [修复] 修复了自动扭蛋功能，无法正常增加期望扭蛋数的问题（#cebb125）
* [新增] 封装滚动处理 & 优化部分任务的滚动逻辑（#c315408）
* [新增] 新增自动清理超过七天的日志文件（#c8a06eb）
* [重构] 重新组织 kotonebot.tasks 模块（#044b1c2）
* [重构] 将 kotonebot.tasks.game_ui 模块拆分为多个文件（#96be974）

框架：
* [新增] 新增 HsvColorRemover 与 HsvColorsRemover 预处理器（#174850d）

其他：
* [其他] 新增调试入口脚本（#1805ff7）
* [其他] 标记一些函数为 deprecated（#0c98e19）

### v2025.3.22.0
脚本：
* [新增] 支持处理培育中技能卡自选删除（#f026f5a）
* [新增] 支持 Windows 平台的启动游戏（#04ddac5）
* [新增] 支持跳过培育结束的演出（#cc73188）
* [新增] 配置文件中引入 windows 截图方式（#0f92c6f）
* [修复] 修复某些情况下无法检测到进行中培育（#9e764c7）
* [修复] 修复某些情况下会跳过未读交流时会发生误触（#46c7354）
* [修复] 修复某些情况下竞赛时会卡在 SKIP 按钮上（#69a5987）
* [修复] 修复进入竞赛时无法跳过赛季奖励（#8356fa1）
* [重构] 将 AutoHotKey.exe 移动到 ksaa-res 包内（#1c063f3）
* [重构] 将培育中 acquisitions 函数所有 OCR 改为模板匹配（#6e336d9）
* [重构] 重构 toolbar_home，提取 toolbar_menu（#425e998）

框架：
* [新增] 将 kotonebot.client.Device 类分为了 AndroidDevice 和 WindowsDevice（#abe9b0a）
* [新增] 引入对 Windows 的支持（#d38bb16）
* [新增] 新增图像预处理器（#b831e9e）
* [修复] 修复首次启动时创建的配置版本号不正确（#aab6fc3）

开发工具：
* [新增] ImageAnnotation 中支持修改现有定义的标注类型（#7bd921b）

其他：
* [其他] 移除无用 import（#f902315）
* [其他] 添加针对 DMM/PC 平台的 `启动（Windows）.bat` 文件（#a86004b）
* [其他] 增加 Windows 平台依赖（#45a9838）
* [其他] 修复 just package 命令未不会自动创建环境的问题（#d08af8f）

### v2025.3.15.2
脚本：
* [修复] 修复培育开始时部分偶像会卡在偶像选择页面的问题（#6429826b）

框架：
* [修复] 修复雷电模拟器的自启动失败问题 & 调整相关UI（#972a4789）

### v2025.3.15.1
脚本：
* [修复] 再次修复对于某些偶像，培育开始时会卡在偶像选择页（#80be65d）

### v2025.3.15.0
脚本：
* [修复] 修复没有配置文件时，升级配置会失败的问题（#7f00eef）
* [修复] 修复扭蛋在慢速设备下的两个问题（#8f7650e）
* [修复] 修复了社团奖励领取后，ocr过早检测的问题（#54787f4）
* [修复] 修复了某些情况下社团奖励领取失效（#76475f4）
* [修复] 修复进入竞赛时有时会发生误触（#facac04）
* [修复] 修复对于某些偶像，培育开始时会卡在偶像选择页（#f13244a）
* [新增] 新增扭蛋机功能，支持任意次数的任意类型扭蛋（#fe3dd29）
* [新增] 新增升级一张低等级支援卡的任务（#a0216ba）
* [新增] 领取社团奖励（笔记请求）和送礼物（#e0cf155）
* [新增] 在配置项中加入跟踪功能的启停（#9949f5b）
* [新增] 竞赛支持选择第几个挑战者(1/2/3)（#6dc4cee）
* [新增] 支持kuyo模拟器启动（#b7805a2）
* [新增] 新增推荐卡检测严格模式（#ea3f931）
* [新增] 新增跟踪功能 & 支持跟踪推荐卡检测（#9a45a9b）
* [重构] 用ocr来区分社团奖励中的笔记请求是否结束（#f0077e3）
* [重构] 将 PIdol 改为 IntEnum（#724ad9f）
* [重构] 用资源文件和HintPoint代替club_reward中硬编码的坐标（#818acb6）
* [重构] 把kuyo启动作为start_game的一个子选项 & 添加包名自定义配置（#179cdfe）
* [重构] 合并 start_game 和 start_kuyo_and_game（#dd225ef）

界面：
* [修复] 修复长多选 Dropdown 在选择一个选项后滚动会自动重置的问题（#3283f15）
* [新增] 改进错误报告上传功能（#252cccf）

框架：
* [新增] create_device 时判断连接结果（#05e471a）
* [新增] 调试图片移除信息改为 VERBOSE 等级（#4afed09）
* [新增] MockDevice 新增支持 mock 图片数据（#1409255）

其他：
* [文档] 修改 README 路线图（#c4affe9）
* [文档] 纠正 REAMDE 中 アノマリー 拼写错误（#7d16bd6）
* [文档] 更新 README（#a140149）
* [其他] 从 git tags 中自动提取版本号（#64ae6ee）
* [其他] 为配置备份文件设置 gitignore（#392c8ab）
* [其他] XcantloadX/kotones-auto-assistant into main（#759e8c3）
* [其他] 将expect_wait前面的if not删去（#0b3e55e）
* [其他] 新增了pnpm-lock，在pnpm包管理器下确保依赖一致（#63e0772）
* [其他] 移除了club_reward.py中一些无用的import（#7dca24a）

### v2025.3.9.4
脚本：
* [修复] 修复培育中卡在考试前的问题

### v2025.3.9.3
其他：
* 降级 numpy<2.0，修复“A module that was compiled using NumPy 1.x cannot be run in NumPy 2.0.0 as it may crash.”问题

### v2025.3.9.1、v2025.3.9.2
其他：
* 降级 onnxruntime==1.14.0，修复“ImportError: DLL load failed while importing onnxruntime_pybind11_state”问题

### v2025.3.9.0
脚本：
* [修复] 修复部分情况下工作分配偶像选择页面报错（#a35e6c3）
* [修复] 修复培育中强化技能卡时有几率卡在确认按钮上（#221721a）
* [新增] 支持指定培育中行动优先级（#fc8c456）
* [新增] 支持处理培育结束时的未读交流（#452ba67）
* [新增] 培育中支持优先选择 SP 课程（#ee3808c）
* [新增] 培育中文化课（授業）行动支持处理自习事件（#47287fd）

界面：
* [新增] UI 上实时展示设备画面（#72a1a17）

框架：
* [修复] 修复调试模式下内存中截图数据不会释放的问题（#bead31a）
* [修复] 修复启动模拟器前不会自动检测是否已启动的问题（#71d75b7）
* [修复] 修复若软件处于中文目录，读取图片资源失败的问题（#a8aee32）
* [新增] 降低双击默认速度（#e63ad2d）
* [新增] 支持启动 kaa 前自动启动模拟器（#8ee51ba）
* [重构] 移动 grayscaled 和 unify_image 函数的位置（#500e11d）
* [重构] 移动 util 模块位置（#0d3e5de）
* [重构] 移除原有 kotonebot.run 模块中函数，改用 Kotonebot 类（#508d81c）

开发工具：
* [修复] 修复写出 dumps 文件时的编码问题（#d5c64df）
* [新增] 优化 DumpViewer 调用堆栈部分与时间部分的显示（#41d4c4c）
* [新增] ImageAnnotation 新增 description 字段（#425057d）
* [新增] DumpViewer 新增滚动锁定按钮（#79927a7）
* [新增] DumpViewer 两次结果之间新增时间差显示（#e3583f2）
* [重构] 将调试 WS 消息体提取为 pydantic Model（#abf7fbc）

其他：
* [其他] R.py 中的 HintBox 类型的注释也加入裁剪后图片的展示（#7907e44）

### v2025.3.1.0
脚本：
* [新增] 新增支持指定回忆编成编号（#6482962）
* [新增] 优化培育与日常流程，修复一些 bug（#145f97f）
* [新增] 支持处理外出行动 & 优化部分流程（#03fdfaa）
* [新增] 新增支持处理技能卡强化选择事件（#133f084）
* [新增] 培育中支持识别睡意 T 卡（眠気）（#5a68500）
* [修复] 修复课程中无法处理当前回合无剩余手牌的情况（#bfb574c）
* [修复] 修复课程中遇到四/五张手牌时会卡住的问题（#ae22253）
* [修复] 修复某些情况下会卡在培育开始第二步（#73dc4d8）
* [修复] 修复某些情况下会卡在工作分配偶像选择页面（#2ff655f）

界面：
* [新增] UI 上新增更新日志 Tab（#07be7b3）
* [新增] 新增一键上传日志报告功能（#62d5805）

框架：
* [新增] device.click() 方法新增支持传入 tuple[int, int]（#f146038）
* [新增] adb_raw 截图方法加入超时自动重试（#08f382c）
* [新增] 为 device.click() 方法添加可视调试信息（#be37164）
* [新增] 为 adb_raw 截图方式的截图线程加入自动重试（#9b25098）
* [修复] 修复了 device.click() 调试信息在没有截图数据时会报错（#f0a3baa）
* [修复] 修复写入调试数据前不会自动清空上次数据的问题（#80e04ee）

其他：
* [新增] ImageAnntation 页面与 Python 代码引入 HintPoint 支持（#949e28d）
* [修复] 修复某些情况下会卡在领取任务奖励处（#f80660e）
* [单测] 移除无用单元测试（#54f9b03）

### v2025.2.23.0
修复：
* 返回首页时无法处理弹出某些弹窗
* 培育中，某些情况下会卡在饮料领取弹窗
* 培育中，某些情况下会在饮料领取时报错

新增：
* 支持处理游戏本体更新弹窗
* 培育中，支持处理技能卡移除事件
* 培育结束时，支持处理“偶像强化月”活动的新纪录弹窗
* UI 标题上新增版本号显示

### v2025.2.20.1
修复：
* ADB 设备连接失败的问题

### v2025.2.20.0
初始版本。