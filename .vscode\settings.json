{
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.testing.unittestArgs": [
        "-v",
        "-s",
        "./tests",
        "-p",
        "test*.py"
    ],
    "python.testing.pytestEnabled": false,
    "python.testing.unittestEnabled": true,
    "basedpyright.analysis.typeCheckingMode": "basic",
    "python.languageServer": "Default",
    "python.analysis.exclude": [
        "submodules",
        "**/__pycache__",
        ".git",
        ".venv",
        "venv",
        "**/node_modules"
    ],
    // "python.analysis.diagnosticMode": "workspace"
}