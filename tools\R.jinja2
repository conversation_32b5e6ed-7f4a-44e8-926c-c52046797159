{%- if not production %}
####### 图片资源文件 #######
####### 此文件为自动生成，请勿编辑 #######
####### AUTO GENERATED. DO NOT EDIT. #######
{%- endif %}
from kotonebot.kaa.common import sprite_path
from kotonebot.backend.core import Image, HintBox, HintPoint


{% macro render_class_attributes(class) -%}
{%- for attr in class.attributes -%}
{%- if attr.type == 'image' %}
    {{ attr.name }} = {{ attr.value }}
    {%- if not production %}
    """
{%- for line in attr.docstring.split('\n') %}
    {{ line }}
{%- endfor -%}
    """
    {%- endif %}
{% elif attr.type == 'class' %}
    class {{ attr.name }}:
    {{ render_class_attributes(attr) | indent(4) }}
        pass
{%- else %}
    {{ attr.name }} = {{ attr.name }}
{%- endif %}
{%- endfor %}
{% endmacro -%}

{%- for class in data %}
class {{ class.name }}:
    {{ render_class_attributes(class) }}
    pass
{%- endfor -%}
