.below-navbar {
    margin-top: 7.5rem;
}

.title {
    font-size: 4.2rem;
    line-height: 1;
}

.subtitle {
    font-size: 2.1rem;
    line-height: 1;
}

.media-container {
    height: auto;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: 0 auto;
}

/* Navbar */

#navbarNav {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 4.5rem;
}

#navbarSticker {
    height: 60px;
    width: 60px;
}

.bi-github:hover {
    color: black !important;
    transition: color 0.3s;
}

/* Home */

#homeFeaturedContainer * {
    max-width: 100%;
}

.media-container-home {
    aspect-ratio: 16 / 9;
}

/* Search */

#eppValue {
    width: 3rem;
    text-align: center;
    pointer-events: none;
    opacity: 1;
}

#searchEntryCard {
    cursor: pointer;
    margin: 0.5rem 0;
}

.media-container-search {
    width: 100%;
    aspect-ratio: 2 / 1;
}

.media-content-search {
    max-height: 10rem;
    object-fit: contain;
}

/* View */

.media-container-view {
    flex-direction: column; /* stack children vertically */
    width: 100%;
    aspect-ratio: 16 / 9;
    border: 2px dotted lightgray;
}

.media-content-view img,
.media-content-view video {
    display: block;
    max-width: 100%;
    max-height: 30rem;
    object-fit: contain;
    object-position: center;
}

.media-content-view audio {
    display: block;
    width: 36rem;
    object-fit: contain;
    object-position: center;
}
