- missionPassId: mission_pass-001
  threshold: 50
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 30000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 150
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hski-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ttmr-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 40000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_fktn-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-1
    quantity: 10
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-001
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2024-06
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 8000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_amao-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ssmk-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 300
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-1
    quantity: 10
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_shro-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 8000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 300
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 8000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 300
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-06
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2024-07
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kllj-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kcna-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hrnm-hair-0001
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-07
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2024-08
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hski-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ssmk-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_shro-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-08
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2024-09
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ttmr-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_amao-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kllj-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-09
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2024-10
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kcna-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hume-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hrnm-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_fktn-hair-0003
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-10
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2024-11
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hski-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 15000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ttmr-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_fktn-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-11
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2024-12
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hume-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_shro-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kcna-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_jsna-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2024-12
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2025-01
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_amao-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 70000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hrnm-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kllj-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ssmk-hair-0004
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-01
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2025-02
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hski-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 20000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ttmr-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 3500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_fktn-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-02
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2025-03
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_jsna-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hume-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 3500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_shro-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kcna-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-03
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2025-04
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_amao-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ssmk-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 3500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kllj-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hrnm-hair-0006
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-04
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2025-05
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hski-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 25000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ttmr-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 3500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_fktn-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_jsna-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-05
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2025-06
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kcna-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_shro-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 35000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hmsz-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 3500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 35000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hume-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 2000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-06
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100
- missionPassId: mission_pass-2025-07
  threshold: 100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 6000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_amao-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 7000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_kllj-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 100000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-gashaticket-10
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1200
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 35000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1300
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_ssmk-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1400
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 600
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 3500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1500
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1500
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1600
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 9000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 35000
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1700
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 700
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1500
  feature: false
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1800
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
  premiumReward:
    resourceType: ResourceType_CostumeHead
    resourceId: costume_head_hrnm-hair-0007
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 1900
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
  premiumReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 2000
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
  premiumReward:
    resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 2000
  feature: true
  repeat: false
  repeatPoint: 0
- missionPassId: mission_pass-2025-07
  threshold: 2100
  normalReward:
    resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
  premiumReward:
    resourceType: ResourceType_Unknown
    resourceId: ""
    quantity: 0
  feature: false
  repeat: true
  repeatPoint: 100