/* Search bars (navbar, home) */

.gentle-button {
    background-color: #fff;
    transition: background-color 0.3s;
    border: none;
    border-radius: 0.5rem;
    padding: 0.25rem 0.5rem;
    color: gray;
    cursor: pointer;
}
.gentle-button:hover {
    background-color: #eee;
}
.gentle-button:active {
    background-color: lightgray;
}

.floating-button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
}

#searchInput {
    padding-right: 3rem; /* don't overlap with icon */
}

#searchButton #homeGotoButton {
    font-size: 1.25rem;
}

#filtersMenu {
    position: absolute;
    right: 0;
    width: 39rem;
    /*  The menu appears both on the navbar and the home page,
        whose search bars have different widths. */
    padding: 0.75rem;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 9999;
}

#filtersCharacterFigure {
    max-width: 100%; /* the figures are quite small */
    padding: 0.1rem;
    overflow: hidden;
    cursor: pointer;
    border-radius: 0.5rem;
    transition: background-color 0.3s;
}
#filtersCharacterFigure:hover {
    background-color: #eee;
}
#filtersCharacterFigure:active {
    background-color: lightgray;
}
/* Fine, we could've used some button class */
