{"name": "kotonebot-devtool", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@types/node": "^22.12.0", "ace-builds": "^1.37.5", "ace-code": "^1.37.5", "ace-linters": "^1.4.1", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "fabric": "^6.5.4", "immer": "^10.1.1", "react": "^18.3.1", "react-ace": "^13.0.0", "react-bootstrap": "^2.10.8", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-router-dom": "^7.1.3", "use-immer": "^0.11.0", "uuid": "^11.0.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}