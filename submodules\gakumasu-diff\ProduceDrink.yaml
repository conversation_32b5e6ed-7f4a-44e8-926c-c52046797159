- id: pdrink_00-1-001
  assetId: img_general_pdrink_1-001
  name: 初星水
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_lesson-0010-01
  rarity: ProduceDrinkRarity_R
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamLesson
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ
    targetId: Label_ExamLesson
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson-0010-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson-0010-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "10"
    targetId: ""
    targetLevel: 0
    effectValue1: 10
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson-0010-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson-0010-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeLessonCountAdd
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ""
    targetId: Description_LessonCountAdd_CountSection
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson-0010-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_lesson-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "100010000001"
- id: pdrink_00-1-004
  assetId: img_general_pdrink_1-004
  name: 烏龍茶
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_block-0007
  rarity: ProduceDrinkRarity_R
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamBlock
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気
    targetId: Label_ExamBlock
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0007
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0007
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "7"
    targetId: ""
    targetLevel: 0
    effectValue1: 7
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0007
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0007
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_block-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "100010000004"
- id: pdrink_00-2-001
  assetId: img_general_pdrink_2-001
  name: ミックススムージー
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_hand_grave_count_card_draw
  - p_drink_effect-e_effect-exam_stamina_recover_fix-0002
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札をすべて入れ替える
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_hand_grave_count_card_draw
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_hand_grave_count_card_draw
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaRecoverFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力回復
    targetId: Label_ExamStaminaRecoverFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 8
  effectGroupIds:
  - effect_group-visible-exam_hand_grave_count_card_draw-000
  - effect_group-visible-stamina_recover_fix-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "200010000001"
- id: pdrink_00-2-003
  assetId: img_general_pdrink_2-003
  name: リカバリドリンク
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_stamina_recover_fix-0006
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaRecoverFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力回復
    targetId: Label_ExamStaminaRecoverFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0006
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0006
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "6"
    targetId: ""
    targetLevel: 0
    effectValue1: 6
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0006
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0006
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 15
  effectGroupIds:
  - effect_group-visible-stamina_recover_fix-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "200010000003"
- id: pdrink_00-2-004
  assetId: img_general_pdrink_2-004
  name: フレッシュビネガー
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
  - p_drink_effect-e_effect-exam_stamina_recover_fix-0003
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札をすべて
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardUpgrade
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン中強化
    targetId: Label_ExamCardUpgrade
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaRecoverFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力回復
    targetId: Label_ExamStaminaRecoverFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 3
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_recover_fix-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_card_upgrade-000
  - effect_group-visible-stamina_recover_fix-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "200010000004"
- id: pdrink_00-2-008
  assetId: img_general_pdrink_2-008
  name: ブーストエキス
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_lesson_value_multiple-0300-03
  - p_drink_effect-e_effect-exam_stamina_consumption_down-03
  - p_drink_effect-e_effect-exam_stamina_reduce_fix-0002
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamLessonValueMultiple
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ上昇量増加
    targetId: Label_ExamLessonValueMultiple
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0300-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0300-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValuePercent1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "30"
    targetId: ""
    targetLevel: 0
    effectValue1: 300
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0300-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "%</nobr>（<nobr>"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0300-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 3
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0300-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0300-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0300-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaConsumptionDown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 消費体力減少
    targetId: Label_ExamStaminaConsumptionDown
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 3
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaReduceFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力消費
    targetId: Label_ExamStaminaReduceFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 55
  effectGroupIds:
  - effect_group-visible-exam_stamina_consumption_down-000
  - effect_group-visible-exam_lesson_value_multiple-000
  - effect_group-visible-stamina_reduce_fix-000
  - effect_group-hidden-stamina_reduce_fix-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "200010090008"
- id: pdrink_00-2-009
  assetId: img_general_pdrink_2-009
  name: パワフル漢方ドリンク
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_card_move-p_card_search-deck_grave-idol-unique-1-hand-all-0_0
  - p_drink_effect-e_effect-exam_stamina_consumption_down-03
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札か捨札にあるアイドル固有スキルカード1枚を
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-idol-unique-1-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札
    targetId: Label_ProduceCardPositionType_Hand
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-idol-unique-1-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: に移動
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-idol-unique-1-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-idol-unique-1-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaConsumptionDown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 消費体力減少
    targetId: Label_ExamStaminaConsumptionDown
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 3
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 60
  effectGroupIds:
  - effect_group-visible-exam_stamina_consumption_down-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "200600240009"
- id: pdrink_00-2-010
  assetId: img_general_pdrink_2-010
  name: センブリソーダ
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_lesson_value_multiple-0100-05
  - p_drink_effect-e_effect-exam_card_draw-0002
  - p_drink_effect-e_effect-exam_stamina_reduce_fix-0002
  - p_drink_effect-e_effect-exam_status_enchant-05-enchant-pdrink_00-2-010-enc01
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamLessonValueMultiple
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ上昇量増加
    targetId: Label_ExamLessonValueMultiple
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0100-05
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0100-05
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValuePercent1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "10"
    targetId: ""
    targetLevel: 0
    effectValue1: 100
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0100-05
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "%</nobr>（<nobr>"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0100-05
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "5"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 5
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0100-05
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0100-05
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_value_multiple-0100-05
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカードを<nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_draw-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_draw-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 枚</nobr>引く
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_draw-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_draw-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaReduceFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力消費
    targetId: Label_ExamStaminaReduceFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 以降の<nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-05-enchant-pdrink_00-2-010-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "5"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 5
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-05-enchant-pdrink_00-2-010-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>の間、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-05-enchant-pdrink_00-2-010-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン開始時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-05-enchant-pdrink_00-2-010-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカードを引く
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-05-enchant-pdrink_00-2-010-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 	
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-05-enchant-pdrink_00-2-010-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 61
  effectGroupIds:
  - effect_group-visible-exam_status_enchant-000
  - effect_group-visible-exam_card_draw-000
  - effect_group-visible-exam_lesson_value_multiple-000
  - effect_group-visible-stamina_reduce_fix-000
  - effect_group-hidden-stamina_reduce_fix-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "200610370010"
- id: pdrink_00-3-001
  assetId: img_general_pdrink_3-001
  name: 初星ホエイプロテイン
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_playable_value_add-01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPlayableValueAdd
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: スキルカード使用数追加
    targetId: Label_ExamPlayableValueAdd_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_playable_value_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_playable_value_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectCount
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 1
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_playable_value_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_playable_value_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_playable_value_add-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "300010000001"
- id: pdrink_00-3-005
  assetId: img_general_pdrink_3-005
  name: 初星スペシャル青汁
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_card_create_search-0001-p_card_search-random-random_pool-p_random_pool-ssr-upgrade_1-1-hand-random-1_1
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ランダムな強化済みスキルカード（SSR）を、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_create_search-0001-p_card_search-random-random_pool-p_random_pool-ssr-upgrade_1-1-hand-random-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札
    targetId: Label_ProduceCardPositionType_Hand
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_create_search-0001-p_card_search-random-random_pool-p_random_pool-ssr-upgrade_1-1-hand-random-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: に
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_create_search-0001-p_card_search-random-random_pool-p_random_pool-ssr-upgrade_1-1-hand-random-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardCreateId
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 生成
    targetId: Label_ExamCardCreateId
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_create_search-0001-p_card_search-random-random_pool-p_random_pool-ssr-upgrade_1-1-hand-random-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 15
  effectGroupIds: []
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "300010000005"
- id: pdrink_00-3-013
  assetId: img_general_pdrink_3-013
  name: 初星スペシャル青汁X
  planType: ProducePlanType_Common
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_force_play_card_search-p_card_search-random-random_pool-p_random_pool-rush_idol_unique_set-ssr-upgrade_1-1-hand-random-1_1
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ランダムな
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_force_play_card_search-p_card_search-random-random_pool-p_random_pool-rush_idol_unique_set-ssr-upgrade_1-1-hand-random-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardCreateId
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 生成
    targetId: Label_ExamCardCreateId
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_force_play_card_search-p_card_search-random-random_pool-p_random_pool-rush_idol_unique_set-ssr-upgrade_1-1-hand-random-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: した強化済み固有スキルカード（SSR）を、コストを消費せず使用
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_force_play_card_search-p_card_search-random-random_pool-p_random_pool-rush_idol_unique_set-ssr-upgrade_1-1-hand-random-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds: []
  originSupportCardId: ""
  libraryHidden: true
  viewStartTime: "0"
  order: "300010340013"
- id: pdrink_01-1-002
  assetId: img_general_pdrink_1-002
  name: ビタミンドリンク
  planType: ProducePlanType_Plan1
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_parameter_buff-03
  rarity: ProduceDrinkRarity_R
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamParameterBuff
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 好調
    targetId: Label_ExamParameterBuff
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 3
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff-03
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_parameter_buff-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "110010000002"
- id: pdrink_01-1-003
  assetId: img_general_pdrink_1-003
  name: アイスコーヒー
  planType: ProducePlanType_Plan1
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_lesson_buff-0003
  rarity: ProduceDrinkRarity_R
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamLessonBuff
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 集中
    targetId: Label_ExamLessonBuff_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_buff-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_buff-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 3
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_buff-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_buff-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_lesson_buff-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "110010000003"
- id: pdrink_01-2-005
  assetId: img_general_pdrink_2-005
  name: スタミナ爆発ドリンク
  planType: ProducePlanType_Plan1
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_parameter_buff_multiple_per_turn-01
  - p_drink_effect-e_effect-exam_block-0009
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamParameterBuffMultiplePerTurn
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 絶好調
    targetId: Label_ExamParameterBuffMultiplePerTurn
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 1
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamBlock
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気
    targetId: Label_ExamBlock
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0009
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0009
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "9"
    targetId: ""
    targetLevel: 0
    effectValue1: 9
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0009
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0009
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 21
  effectGroupIds:
  - effect_group-visible-exam_block-000
  - effect_group-visible-exam_parameter_buff_multiple_per_turn-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "210010000005"
- id: pdrink_01-3-002
  assetId: img_general_pdrink_3-002
  name: 厳選初星マキアート
  planType: ProducePlanType_Plan1
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_status_enchant-inf-enchant-pdrink_01-3-002-enc01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 以降、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_01-3-002-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン終了時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_01-3-002-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamLessonBuff
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 集中
    targetId: Label_ExamLessonBuff_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_01-3-002-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_01-3-002-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 1
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_01-3-002-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_01-3-002-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 	
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_01-3-002-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 27
  effectGroupIds:
  - effect_group-visible-exam_status_enchant-000
  - effect_group-visible-exam_lesson_buff-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "310010000002"
- id: pdrink_01-3-004
  assetId: img_general_pdrink_3-004
  name: 初星ブーストエナジー
  planType: ProducePlanType_Plan1
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_parameter_buff_multiple_per_turn-02
  - p_drink_effect-e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamParameterBuffMultiplePerTurn
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 絶好調
    targetId: Label_ExamParameterBuffMultiplePerTurn
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-02
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-02
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 2
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-02
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-02
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_parameter_buff_multiple_per_turn-02
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札をすべて
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardUpgrade
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: レッスン中強化
    targetId: Label_ExamCardUpgrade
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_upgrade-p_card_search-hand-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 26
  effectGroupIds:
  - effect_group-visible-exam_card_upgrade-000
  - effect_group-visible-exam_parameter_buff_multiple_per_turn-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "310010000004"
- id: pdrink_01-3-012
  assetId: img_general_pdrink_3-012
  name: 初星黒酢
  planType: ProducePlanType_Plan1
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_card_move-p_card_search-deck_grave-hand-select-1_1
  - p_drink_effect-e_effect-exam_search_play_card_stamina_consumption_change-02-inf-p_card_search-deck_all-all-0_0
  - p_drink_effect-e_effect-exam_stamina_reduce_fix-0002
  - p_drink_effect-e_effect-exam_stamina_consumption_add-01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札か捨札にあるスキルカードを選択し、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-hand-select-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescriptionName
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 手札
    targetId: Label_ProduceCardPositionType_Hand
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-hand-select-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: に移動
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-hand-select-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-hand-select-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 次に使用したスキルカードの消費体力を<nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_search_play_card_stamina_consumption_change-02-inf-p_card_search-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "0"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_search_play_card_stamina_consumption_change-02-inf-p_card_search-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>にする（<nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_search_play_card_stamina_consumption_change-02-inf-p_card_search-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectCount
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 2
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_search_play_card_stamina_consumption_change-02-inf-p_card_search-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 回</nobr>）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_search_play_card_stamina_consumption_change-02-inf-p_card_search-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_search_play_card_stamina_consumption_change-02-inf-p_card_search-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaReduceFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力消費
    targetId: Label_ExamStaminaReduceFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaConsumptionAdd
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 消費体力増加
    targetId: Label_ExamStaminaConsumptionAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 1
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 61
  effectGroupIds:
  - effect_group-visible-exam_stamina_consumption_add-000
  - effect_group-visible-stamina_reduce_fix-000
  - effect_group-hidden-stamina_reduce_fix-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "310610370012"
- id: pdrink_02-1-006
  assetId: img_general_pdrink_1-006
  name: ルイボスティー
  planType: ProducePlanType_Plan2
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_review-0003
  rarity: ProduceDrinkRarity_R
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamReview
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 好印象
    targetId: Label_ExamReview_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_review-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_review-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 3
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_review-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_review-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_review-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "120010000006"
- id: pdrink_02-1-008
  assetId: img_general_pdrink_1-008
  name: ホットコーヒー
  planType: ProducePlanType_Plan2
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_card_play_aggressive-0003
  rarity: ProduceDrinkRarity_R
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardPlayAggressive
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: やる気
    targetId: Label_ExamCardPlayAggressive_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_play_aggressive-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_play_aggressive-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 3
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_play_aggressive-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_play_aggressive-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_card_play_aggressive-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "120010000008"
- id: pdrink_02-2-006
  assetId: img_general_pdrink_2-006
  name: おしゃれハーブティー
  planType: ProducePlanType_Plan2
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_lesson_depend_exam_review-1000-01
  - p_drink_effect-e_effect-exam_block-0003
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamReview
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 好印象
    targetId: Label_ExamReview_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-1000-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: の<nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-1000-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValuePercent1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "100"
    targetId: ""
    targetLevel: 0
    effectValue1: 1000
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-1000-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "%</nobr>分"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-1000-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamLesson
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: パラメータ
    targetId: Label_ExamLesson
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-1000-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 上昇
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-1000-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeLessonCountAdd
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ""
    targetId: Description_LessonCountAdd_CountSection
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-1000-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_lesson_depend_exam_review-1000-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamBlock
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 元気
    targetId: Label_ExamBlock
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "3"
    targetId: ""
    targetLevel: 0
    effectValue1: 3
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_block-0003
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 21
  effectGroupIds:
  - effect_group-visible-exam_lesson_depend_exam_review-000
  - effect_group-visible-exam_lesson-000
  - effect_group-visible-exam_block-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "220010000006"
- id: pdrink_02-3-006
  assetId: img_general_pdrink_3-006
  name: 厳選初星ティー
  planType: ProducePlanType_Plan2
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-006-enc01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 以降、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-006-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン終了時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-006-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamReview
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 好印象
    targetId: Label_ExamReview_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-006-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-006-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 1
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-006-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-006-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 	
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-006-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 27
  effectGroupIds:
  - effect_group-visible-exam_status_enchant-000
  - effect_group-visible-exam_review-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "320010000006"
- id: pdrink_02-3-007
  assetId: img_general_pdrink_3-007
  name: 厳選初星ブレンド
  planType: ProducePlanType_Plan2
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-007-enc01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 以降、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-007-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン終了時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-007-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamCardPlayAggressive
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: やる気
    targetId: Label_ExamCardPlayAggressive_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-007-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-007-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 1
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-007-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-007-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 	
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_02-3-007-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 26
  effectGroupIds:
  - effect_group-visible-exam_status_enchant-000
  - effect_group-visible-exam_card_play_aggressive-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "320010000007"
- id: pdrink_02-3-010
  assetId: img_general_pdrink_3-010
  name: 特製ハツボシエキス
  planType: ProducePlanType_Plan2
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
  - p_drink_effect-e_effect-exam_stamina_reduce_fix-0002
  - p_drink_effect-e_effect-exam_stamina_consumption_add-01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 次に使用する
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceCardCategory
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_ActiveSkill
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: アクティブスキルカード
    targetId: Label_ActiveSkillCard
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: の効果をもう<nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 1
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 回</nobr>発動（<nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectCount
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 1
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 回</nobr>・<nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 1
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>）
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_search_effect_play_count_buff-0001-01-01-p_card_search-active_skill-deck_all-all-0_0
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaReduceFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力消費
    targetId: Label_ExamStaminaReduceFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaConsumptionAdd
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 消費体力増加
    targetId: Label_ExamStaminaConsumptionAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 1
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 55
  effectGroupIds:
  - effect_group-visible-exam_card_search_effect_play_count_buff-000
  - effect_group-visible-exam_stamina_consumption_add-000
  - effect_group-visible-stamina_reduce_fix-000
  - effect_group-hidden-stamina_reduce_fix-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "320010090010"
- id: pdrink_03-1-009
  assetId: img_general_pdrink_1-009
  name: ジンジャーエール
  planType: ProducePlanType_Plan3
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_concentration-0001
  - p_drink_effect-e_effect-exam_full_power_point-0001
  rarity: ProduceDrinkRarity_R
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_concentration-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: に変更
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_concentration-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_concentration-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPowerPoint
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力値
    targetId: Label_ExamFullPowerPoint
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_full_power_point-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_full_power_point-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 1
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_full_power_point-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_full_power_point-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_concentration-000
  - effect_group-visible-exam_full_power-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "130010240009"
- id: pdrink_03-1-010
  assetId: img_general_pdrink_1-010
  name: ほうじ茶
  planType: ProducePlanType_Plan3
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_preservation-0001
  - p_drink_effect-e_effect-exam_full_power_point-0002
  rarity: ProduceDrinkRarity_R
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_preservation-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: に変更
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_preservation-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_preservation-0001
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPowerPoint
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力値
    targetId: Label_ExamFullPowerPoint
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_full_power_point-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_full_power_point-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_full_power_point-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_full_power_point-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 0
  effectGroupIds:
  - effect_group-visible-exam_preservation-000
  - effect_group-visible-exam_full_power-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "130010240010"
- id: pdrink_03-2-007
  assetId: img_general_pdrink_2-007
  name: ほっと緑茶
  planType: ProducePlanType_Plan3
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_card_move-p_card_search-deck_grave-hold-select-1_1
  - p_drink_effect-e_effect-exam_preservation-0002
  rarity: ProduceDrinkRarity_Sr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 山札か捨札にあるスキルカードを選択し、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-hold-select-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceDescription
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 保留
    targetId: Label_ProduceCardPositionType_Hold
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-hold-select-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: に移動
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-hold-select-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_card_move-p_card_search-deck_grave-hold-select-1_1
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamPreservation
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 温存
    targetId: Label_ExamPreservation_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_preservation-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_preservation-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 段階目に変更
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_preservation-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 21
  effectGroupIds:
  - effect_group-visible-exam_preservation-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "230210240007"
- id: pdrink_03-3-008
  assetId: img_general_pdrink_3-008
  name: 厳選初星チャイ
  planType: ProducePlanType_Plan3
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_status_enchant-inf-enchant-pdrink_03-3-008-enc01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 以降、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_03-3-008-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン開始時、
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_03-3-008-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamFullPowerPoint
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 全力値
    targetId: Label_ExamFullPowerPoint
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_03-3-008-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_03-3-008-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 1
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_03-3-008-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_03-3-008-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 	
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_status_enchant-inf-enchant-pdrink_03-3-008-enc01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 27
  effectGroupIds:
  - effect_group-visible-exam_status_enchant-000
  - effect_group-visible-exam_full_power-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "330270240008"
- id: pdrink_03-3-009
  assetId: img_general_pdrink_3-009
  name: 初星スーパーソーダ
  planType: ProducePlanType_Plan3
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_concentration-0002
  - p_drink_effect-e_effect-exam_stamina_consumption_down-01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamConcentration
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 強気
    targetId: Label_ExamConcentration_Produce
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_concentration-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_concentration-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 段階目に変更
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_concentration-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_concentration-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaConsumptionDown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 消費体力減少
    targetId: Label_ExamStaminaConsumptionDown
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 1
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_down-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 26
  effectGroupIds:
  - effect_group-visible-exam_stamina_consumption_down-000
  - effect_group-visible-exam_concentration-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "330260240009"
- id: pdrink_03-3-011
  assetId: img_general_pdrink_3-011
  name: 初星湯
  planType: ProducePlanType_Plan3
  produceDrinkEffectIds:
  - p_drink_effect-e_effect-exam_extra_turn
  - p_drink_effect-e_effect-exam_stamina_reduce_fix-0002
  - p_drink_effect-e_effect-exam_stamina_consumption_add-01
  rarity: ProduceDrinkRarity_Ssr
  produceDescriptions:
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamExtraTurn
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン追加
    targetId: Label_ExamExtraTurn
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_extra_turn
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>+1</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_extra_turn
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_extra_turn
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaReduceFix
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 体力消費
    targetId: Label_ExamStaminaReduceFix
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeEffectValue1
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "2"
    targetId: ""
    targetLevel: 0
    effectValue1: 2
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: </nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: |

    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_reduce_fix-0002
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_ProduceExamEffectType
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_ExamStaminaConsumptionAdd
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: 消費体力増加
    targetId: Label_ExamStaminaConsumptionAdd
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: <nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_Exam
    examDescriptionType: ExamDescriptionType_CustomizeTurn
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: "1"
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 1
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  - produceDescriptionType: ProduceDescriptionType_PlainText
    examDescriptionType: ExamDescriptionType_Unknown
    examEffectType: ProduceExamEffectType_Unknown
    produceCardGrowEffectType: ProduceCardGrowEffectType_Unknown
    produceCardCategory: ProduceCardCategory_Unknown
    produceCardMovePositionType: ProduceCardMovePositionType_Unknown
    produceStepType: ProduceStepType_Unknown
    text: ターン</nobr>
    targetId: ""
    targetLevel: 0
    effectValue1: 0
    effectValue2: 0
    effectCount: 0
    turn: 0
    costValue: 0
    produceDescriptionSwapId: ""
    originProduceExamTriggerId: ""
    originProduceExamEffectId: e_effect-exam_stamina_consumption_add-01
    originProduceCardStatusEnchantId: ""
    isCost: false
    isOnlyOutGame: false
    changeColor: false
  unlockProducerLevel: 60
  effectGroupIds:
  - effect_group-visible-exam_extra_turn-000
  - effect_group-visible-exam_stamina_consumption_add-000
  - effect_group-visible-stamina_reduce_fix-000
  - effect_group-hidden-stamina_reduce_fix-000
  originSupportCardId: ""
  libraryHidden: false
  viewStartTime: "0"
  order: "330600240011"