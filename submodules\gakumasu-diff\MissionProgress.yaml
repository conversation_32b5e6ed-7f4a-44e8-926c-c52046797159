- missionId: dearness_event_ssmk-001
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2500
- missionId: dearness_event_ssmk-002
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 2500
- missionId: dearness_event_ssmk-003
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1250
- missionId: dearness_event_ssmk-004
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1250
- missionId: dearness_event_ssmk-005
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2-a-2
    quantity: 200
- missionId: dearness_event_ssmk-006
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2-a-3
    quantity: 200
- missionId: dearness_event_ssmk-007
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: dearness_event_ssmk-008
  threshold: 8
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: dearness_normal-mission_ssmk-001
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 50
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
- missionId: dearness_normal-mission_ssmk-002
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: dearness_normal-mission_ssmk-002
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: dearness_normal-mission_ssmk-002
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: dearness_normal-mission_ssmk-002
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 20
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: dearness_normal-mission_ssmk-003
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: dearness_normal-mission_ssmk-003
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: dearness_normal-mission_ssmk-003
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: dearness_normal-mission_ssmk-003
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 20
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
- missionId: dearness_normal-mission_ssmk-004
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2-a-2
    quantity: 20
- missionId: dearness_normal-mission_ssmk-004
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 20
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2-a-2
    quantity: 40
- missionId: dearness_normal-mission_ssmk-004
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 20
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2-a-2
    quantity: 40
- missionId: dearness_normal-mission_ssmk-005
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2-a-3
    quantity: 30
- missionId: dearness_normal-mission_ssmk-005
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2-a-3
    quantity: 30
- missionId: dearness_normal-mission_ssmk-005
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 10
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2-a-3
    quantity: 40
- missionId: dearness_normal-mission_ssmk-005
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_DearnessPoint
    resourceId: ssmk
    quantity: 20
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: hidden_mission-app-review-main-task-clear
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-amao-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-fktn-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-hrnm-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-hski-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-kcna-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-kllj-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-main-task-clear-01-001
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-amao-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-fktn-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-hmsz-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-hrnm-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-hski-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-hume-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-jsna-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-kcna-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-kllj-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-shro-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-ssmk-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-produce-clear-i_card-ttmr-3-000-b
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-shro-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-ssmk-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-normal-ttmr-piece-pack-remind
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-produce-audition-failure_kcna_initial-card
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-produce-audition-failure_shro_initial-card
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_amao-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_fktn-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_hmsz-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_hrnm-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_hski-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_hume-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_jsna-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_kcna-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_kllj-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_shro-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_ssmk-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-tower_ttmr-clear_star_40
  threshold: 40
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-hmsz
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-amao
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-fktn
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-hmsz
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-hrnm
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-hski
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-hume
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-jsna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-kcna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-kllj
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-shro
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-ssmk
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-a-produce_group-002-ttmr
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-b-hmsz
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-c-hmsz
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-amao
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-fktn
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-hrnm
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-hski
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-hume
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-jsna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-kcna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-kllj
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-shro
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-ssmk
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-a-ttmr
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-amao
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-fktn
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-hrnm
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-hski
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-hume
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-jsna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-kcna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-kllj
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-shro
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-ssmk
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-b-ttmr
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-amao
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-fktn
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-hrnm
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-hski
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-hume
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-jsna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-kcna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-kllj
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-shro
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-ssmk
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-normal-c-ttmr
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-amao
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-fktn
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-hmsz
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-hrnm
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-hski
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-hume
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-jsna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-kcna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-kllj
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-amao
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-fktn
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-hmsz
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-hrnm
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-hski
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-hume
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-jsna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-kcna
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-kllj
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-shro
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-ssmk
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-produce_group-002-ttmr
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-shro
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-ssmk
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-ending-true-ttmr
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-gasha-00420-free-10-004
  threshold: 100
  missionPoint: 0
  rewards: []
- missionId: hidden_mission-view-gasha-00420-ssr-idol-support-1
  threshold: 20
  missionPoint: 0
  rewards: []
- missionId: mission-box-gasha-event-001-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_2
  threshold: 25
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-001-mission_3
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-001-mission_3
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-001-mission_3
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-001-mission_3
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-001-mission_4
  threshold: 25000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_4
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_4
  threshold: 75000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_4
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_4
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_4
  threshold: 200000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_4
  threshold: 250000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_5
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_5
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-001-mission_5
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-001
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_2
  threshold: 25
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-002-mission_3
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-002-mission_3
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-002-mission_3
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-002-mission_3
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-002-mission_4
  threshold: 25000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_4
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_4
  threshold: 75000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_4
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_4
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_4
  threshold: 200000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_4
  threshold: 250000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_5
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_5
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-002-mission_5
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-002
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-003-mission_3
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-003-mission_3
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-003-mission_3
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-003-mission_3
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-003-mission_4
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_4
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_4
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_4
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_4
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_4
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_4
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_5
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_5
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_5
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-003-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-003
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-004-mission_3
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-004-mission_3
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-004-mission_3
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-004-mission_3
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-004-mission_4
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_4
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_4
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_4
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_4
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_4
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_4
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_5
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_5
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_5
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-004-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-004
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-005-mission_3
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-005-mission_3
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-005-mission_3
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-005-mission_3
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-005-mission_4
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_4
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_4
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_4
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_4
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_4
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_4
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_5
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_5
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_5
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-005-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-005
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-006-mission_3
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-006-mission_3
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-006-mission_3
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-006-mission_3
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-006-mission_4
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_4
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_4
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_4
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_4
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_4
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_4
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_5
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_5
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_5
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-006-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-006
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-007-mission_3
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-007-mission_3
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-007-mission_3
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-007-mission_3
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-007-mission_4
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_4
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_4
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_4
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_4
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_4
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_4
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_5
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_5
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_5
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-007-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-007
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-015-mission_3
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-015-mission_3
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-015-mission_3
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-015-mission_3
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-box-gasha-event-015-mission_4
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_4
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_4
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_4
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_4
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_4
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_4
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_5
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_5
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_5
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-box-gasha-event-015-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-box-gasha-event-015
    quantity: 20
- missionId: mission-circle-battle-event-001-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-circle-battle-event-001-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-circle-battle-event-001-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: mission-circle-battle-event-001-mission_2
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_2
  threshold: 4
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_3
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_3
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_3
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_3
  threshold: 4
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_3
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_4
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_4
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_4
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_4
  threshold: 4
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_4
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_5
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_5
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_5
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 50
- missionId: mission-circle-battle-event-001-mission_5
  threshold: 4
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-circle-battle-event-001-mission_5
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-comeback_daily_release_mission_001-mission_11
  threshold: 15
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-comeback_daily_release_mission_001-mission_12
  threshold: 3
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 100
- missionId: mission-comeback_daily_release_mission_001-mission_13
  threshold: 2
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
- missionId: mission-comeback_daily_release_mission_001-mission_14
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_15
  threshold: 5
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
- missionId: mission-comeback_daily_release_mission_001-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
- missionId: mission-comeback_daily_release_mission_001-mission_21
  threshold: 30
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-comeback_daily_release_mission_001-mission_22
  threshold: 6
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 100
- missionId: mission-comeback_daily_release_mission_001-mission_23
  threshold: 4
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
- missionId: mission-comeback_daily_release_mission_001-mission_24
  threshold: 2
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_25
  threshold: 10
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
- missionId: mission-comeback_daily_release_mission_001-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_31
  threshold: 50
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-comeback_daily_release_mission_001-mission_32
  threshold: 9
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 200
- missionId: mission-comeback_daily_release_mission_001-mission_33
  threshold: 6
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
- missionId: mission-comeback_daily_release_mission_001-mission_34
  threshold: 3
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_35
  threshold: 25000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
- missionId: mission-comeback_daily_release_mission_001-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_41
  threshold: 70
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-comeback_daily_release_mission_001-mission_42
  threshold: 12
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 200
- missionId: mission-comeback_daily_release_mission_001-mission_43
  threshold: 8
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
- missionId: mission-comeback_daily_release_mission_001-mission_44
  threshold: 4
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_45
  threshold: 35000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
- missionId: mission-comeback_daily_release_mission_001-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 30000
- missionId: mission-comeback_daily_release_mission_001-mission_51
  threshold: 90
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 2
- missionId: mission-comeback_daily_release_mission_001-mission_52
  threshold: 15
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 200
- missionId: mission-comeback_daily_release_mission_001-mission_53
  threshold: 10
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
- missionId: mission-comeback_daily_release_mission_001-mission_54
  threshold: 5
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_55
  threshold: 45000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
- missionId: mission-comeback_daily_release_mission_001-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_61
  threshold: 110
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 2
- missionId: mission-comeback_daily_release_mission_001-mission_62
  threshold: 18
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 200
- missionId: mission-comeback_daily_release_mission_001-mission_63
  threshold: 12
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
- missionId: mission-comeback_daily_release_mission_001-mission_64
  threshold: 6
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_65
  threshold: 55000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
- missionId: mission-comeback_daily_release_mission_001-mission_66
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: mission-comeback_daily_release_mission_001-mission_71
  threshold: 130
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 2
- missionId: mission-comeback_daily_release_mission_001-mission_72
  threshold: 21
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
- missionId: mission-comeback_daily_release_mission_001-mission_73
  threshold: 14
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 50000
- missionId: mission-comeback_daily_release_mission_001-mission_74
  threshold: 7
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 1000
- missionId: mission-comeback_daily_release_mission_001-mission_75
  threshold: 65000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
- missionId: mission-comeback_daily_release_mission_001-mission_76
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 10
- missionId: mission-comeback_daily_release_mission_001-mission_77
  threshold: 6
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: mission-daily_mission_NIA_clear-mission_11
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: mission-daily_mission_NIA_clear-mission_12
  threshold: 50
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-daily_mission_NIA_clear-mission_13
  threshold: 10000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_14
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: mission-daily_mission_NIA_clear-mission_15
  threshold: 10
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 2
- missionId: mission-daily_mission_NIA_clear-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_21
  threshold: 2
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: mission-daily_mission_NIA_clear-mission_22
  threshold: 100
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-daily_mission_NIA_clear-mission_23
  threshold: 20000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_24
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: mission-daily_mission_NIA_clear-mission_25
  threshold: 12
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 2
- missionId: mission-daily_mission_NIA_clear-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_31
  threshold: 3
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: mission-daily_mission_NIA_clear-mission_32
  threshold: 150
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-daily_mission_NIA_clear-mission_33
  threshold: 30000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_34
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: mission-daily_mission_NIA_clear-mission_35
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 2
- missionId: mission-daily_mission_NIA_clear-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_41
  threshold: 4
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: mission-daily_mission_NIA_clear-mission_42
  threshold: 200
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-daily_mission_NIA_clear-mission_43
  threshold: 40000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_44
  threshold: 15
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: mission-daily_mission_NIA_clear-mission_45
  threshold: 3
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 2
- missionId: mission-daily_mission_NIA_clear-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_51
  threshold: 5
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 40000
- missionId: mission-daily_mission_NIA_clear-mission_52
  threshold: 250
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-daily_mission_NIA_clear-mission_53
  threshold: 50000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-daily_mission_NIA_clear-mission_54
  threshold: 17
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_55
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 2
- missionId: mission-daily_mission_NIA_clear-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_61
  threshold: 6
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 40000
- missionId: mission-daily_mission_NIA_clear-mission_62
  threshold: 300
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-daily_mission_NIA_clear-mission_63
  threshold: 60000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-daily_mission_NIA_clear-mission_64
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_65
  threshold: 3
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: mission-daily_mission_NIA_clear-mission_66
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_71
  threshold: 7
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 40000
- missionId: mission-daily_mission_NIA_clear-mission_72
  threshold: 350
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-daily_mission_NIA_clear-mission_73
  threshold: 70000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-daily_mission_NIA_clear-mission_74
  threshold: 2
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 200
- missionId: mission-daily_mission_NIA_clear-mission_75
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: mission-daily_mission_NIA_clear-mission_76
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: mission-daily-incrementgashadrawcount-1
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-daily-incrementmoneyexchangecount-1
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-daily-incrementproduceclearcount-1
  threshold: 1
  missionPoint: 40
  rewards: []
- missionId: mission-daily-incrementpvpratewincount-1
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-daily-incrementreceivemoney-1
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-daily-incrementsupportcardenhancecount-1
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-daily-incrementworkcount-1
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-highscore-event-001-mission_11
  threshold: 40
  missionPoint: 30
  rewards: []
- missionId: mission-highscore-event-001-mission_11_1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-001-mission_11_1
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-001-mission_11_1
  threshold: 120
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-001-mission_11_1
  threshold: 160
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-001-mission_11_1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-001-mission_11_1
  threshold: 240
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-001-mission_12
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-highscore-event-001-mission_12_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-highscore-event-001-mission_13
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-highscore-event-001-mission_13_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-highscore-event-001-mission_14
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-highscore-event-001-mission_14_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-highscore-event-001-mission_15
  threshold: 5000
  missionPoint: 10
  rewards: []
- missionId: mission-highscore-event-001-mission_15_1
  threshold: 5000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-001-mission_15_1
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-001-mission_15_1
  threshold: 15000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-001-mission_16
  threshold: 5000
  missionPoint: 10
  rewards: []
- missionId: mission-highscore-event-001-mission_16_1
  threshold: 5000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-001-mission_16_1
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-001-mission_16_1
  threshold: 15000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-001-mission_17
  threshold: 5000
  missionPoint: 10
  rewards: []
- missionId: mission-highscore-event-001-mission_17_1
  threshold: 5000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-001-mission_17_1
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-001-mission_17_1
  threshold: 15000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-002-mission_11
  threshold: 40
  missionPoint: 30
  rewards: []
- missionId: mission-highscore-event-002-mission_11_1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-002-mission_11_1
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-002-mission_11_1
  threshold: 120
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-002-mission_11_1
  threshold: 160
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-002-mission_11_1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-002-mission_11_1
  threshold: 240
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 50
- missionId: mission-highscore-event-002-mission_12
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-highscore-event-002-mission_12_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-highscore-event-002-mission_13
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-highscore-event-002-mission_13_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-highscore-event-002-mission_14
  threshold: 1
  missionPoint: 20
  rewards: []
- missionId: mission-highscore-event-002-mission_14_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-highscore-event-002-mission_15
  threshold: 5000
  missionPoint: 10
  rewards: []
- missionId: mission-highscore-event-002-mission_15_1
  threshold: 5000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-002-mission_15_1
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-002-mission_15_1
  threshold: 15000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-002-mission_16
  threshold: 5000
  missionPoint: 10
  rewards: []
- missionId: mission-highscore-event-002-mission_16_1
  threshold: 5000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-002-mission_16_1
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-002-mission_16_1
  threshold: 15000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-002-mission_17
  threshold: 5000
  missionPoint: 10
  rewards: []
- missionId: mission-highscore-event-002-mission_17_1
  threshold: 5000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-002-mission_17_1
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-highscore-event-002-mission_17_1
  threshold: 15000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-003-mission_11
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-highscore-event-003-mission_12
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: mission-highscore-event-003-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-003-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-003-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-003-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-003-mission_21
  threshold: 3
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 50
- missionId: mission-highscore-event-003-mission_22
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 50
- missionId: mission-highscore-event-003-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-003-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-003-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-003-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-003-mission_31
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-highscore-event-003-mission_32
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: mission-highscore-event-003-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-003-mission_41
  threshold: 6
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 50
- missionId: mission-highscore-event-003-mission_42
  threshold: 3
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 50
- missionId: mission-highscore-event-003-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-003-mission_51
  threshold: 8
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 50
- missionId: mission-highscore-event-003-mission_52
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 50
- missionId: mission-highscore-event-003-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-003-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: mission-highscore-event-004-mission_11
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-highscore-event-004-mission_12
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: mission-highscore-event-004-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-004-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-004-mission_15
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-004-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-004-mission_21
  threshold: 3
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-highscore-event-004-mission_22
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: mission-highscore-event-004-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-004-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-004-mission_25
  threshold: 30000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-highscore-event-004-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-004-mission_31
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-highscore-event-004-mission_32
  threshold: 100
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: mission-highscore-event-004-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_35
  threshold: 45000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-004-mission_41
  threshold: 6
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-highscore-event-004-mission_42
  threshold: 3
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: mission-highscore-event-004-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_45
  threshold: 60000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-highscore-event-004-mission_51
  threshold: 8
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-highscore-event-004-mission_52
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: mission-highscore-event-004-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_55
  threshold: 75000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-highscore-event-004-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: mission-highscorerush-event-001-mission_11
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_12
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-001-mission_21
  threshold: 3
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_22
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-001-mission_31
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_32
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-001-mission_41
  threshold: 6
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_42
  threshold: 3
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-001-mission_51
  threshold: 8
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_52
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-001
    quantity: 200
- missionId: mission-highscorerush-event-001-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-002-mission_11
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_12
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_13
  threshold: 5000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_14
  threshold: 5000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_16
  threshold: 4
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-002-mission_21
  threshold: 3
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_22
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_23
  threshold: 10000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_24
  threshold: 10000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_26
  threshold: 4
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-002-mission_31
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_32
  threshold: 1
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_33
  threshold: 15000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_34
  threshold: 15000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_36
  threshold: 4
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-002-mission_41
  threshold: 6
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_42
  threshold: 3
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_43
  threshold: 20000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_44
  threshold: 20000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_46
  threshold: 4
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-highscorerush-event-002-mission_51
  threshold: 8
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_52
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_53
  threshold: 25000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_54
  threshold: 25000
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-coin-highscorerush-event-002
    quantity: 250
- missionId: mission-highscorerush-event-002-mission_56
  threshold: 4
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-001-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-001-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_22
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-001-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_32
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-001-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_42
  threshold: 60000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-001-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_52
  threshold: 80000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-001
    quantity: 30
- missionId: mission-main-story-event-001-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-001
    quantity: 3000
- missionId: mission-main-story-event-001-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-002-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-002-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_22
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-002-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_32
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-002-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_42
  threshold: 60000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-002-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_52
  threshold: 80000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-002
    quantity: 30
- missionId: mission-main-story-event-002-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-002
    quantity: 3000
- missionId: mission-main-story-event-002-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-003-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-003-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_22
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-003-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_32
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-003-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_42
  threshold: 60000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-003-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_52
  threshold: 80000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-003
    quantity: 30
- missionId: mission-main-story-event-003-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-003
    quantity: 3000
- missionId: mission-main-story-event-003-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-004-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-004-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_22
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-004-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_32
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-004-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_42
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-004-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_52
  threshold: 40000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-004
    quantity: 30
- missionId: mission-main-story-event-004-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-004
    quantity: 3000
- missionId: mission-main-story-event-004-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-main-story-event-005-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-005-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_22
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-005-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_32
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-005-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_42
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-005-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_52
  threshold: 40000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-005
    quantity: 30
- missionId: mission-main-story-event-005-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-005
    quantity: 3000
- missionId: mission-main-story-event-005-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-006-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-006-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_22
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-006-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_32
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-006-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_42
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-006-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_52
  threshold: 40000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-006
    quantity: 30
- missionId: mission-main-story-event-006-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-006
    quantity: 3000
- missionId: mission-main-story-event-006-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-007-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-007-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_22
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-007-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_32
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-007-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_42
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-007-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_52
  threshold: 40000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-007
    quantity: 30
- missionId: mission-main-story-event-007-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-007
    quantity: 3000
- missionId: mission-main-story-event-007-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-008-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-008-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_22
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-008-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_32
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-008-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_42
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-008-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_52
  threshold: 40000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-008
    quantity: 30
- missionId: mission-main-story-event-008-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-008
    quantity: 3000
- missionId: mission-main-story-event-008-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-009-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-009-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_22
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-009-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_32
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-009-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_42
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-009-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_52
  threshold: 40000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-009
    quantity: 30
- missionId: mission-main-story-event-009-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-009
    quantity: 3000
- missionId: mission-main-story-event-009-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-010-mission_11
  threshold: 50
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_12
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_13
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_14
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_15
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_16
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-010-mission_21
  threshold: 100
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_22
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_23
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_24
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_25
  threshold: 10000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_26
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-010-mission_31
  threshold: 150
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_32
  threshold: 5000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_33
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_34
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_35
  threshold: 15000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_36
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-010-mission_41
  threshold: 200
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_42
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_43
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_44
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_45
  threshold: 20000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_46
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-main-story-event-010-mission_51
  threshold: 250
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_52
  threshold: 40000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-main-story-event-010
    quantity: 30
- missionId: mission-main-story-event-010-mission_53
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_54
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_55
  threshold: 25000
  missionPoint: 3
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: main-story-event-010
    quantity: 3000
- missionId: mission-main-story-event-010-mission_56
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutedearnesslevel-1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutedearnesslevel-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutedearnesslevel-1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutedearnesslevel-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutefancount-1
  threshold: 2000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-absolutefancount-1
  threshold: 25000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-absolutefancount-1
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-absolutefancount-1
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-absolutefancount-1
  threshold: 250000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-absolutefancount-1
  threshold: 400000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-absolutefancount-1
  threshold: 650000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-absolutefancount-1
  threshold: 1000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-absolutefancount-1
  threshold: 1500000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-absolutefancount-1
  threshold: 2000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-absolutefancount-1
  threshold: 3000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-absolutefancount-1
  threshold: 4000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-absolutefancount-1
  threshold: 5000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3500
- missionId: mission-normal-absolutefancount-1
  threshold: 6000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3500
- missionId: mission-normal-absolutefancount-1
  threshold: 7000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3500
- missionId: mission-normal-absolutefancount-1
  threshold: 8000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3500
- missionId: mission-normal-absolutefancount-1
  threshold: 9000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3500
- missionId: mission-normal-absolutefancount-1
  threshold: 10000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-absolutefancount-1
  threshold: 12000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-absolutefancount-1
  threshold: 14000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-absolutefancount-1
  threshold: 16000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-absolutefancount-1
  threshold: 18000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 25
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 35
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 45
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 55
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 65
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 75
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 85
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardcount-1
  threshold: 90
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absoluteidolcardpotentialrankcount-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absoluteidolcardpotentialrankcount-2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absoluteidolcardpotentialrankcount-3
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absoluteidolcardpotentialrankcount-4
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutepvprategrade-2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 400
- missionId: mission-normal-absolutepvprategrade-3
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
- missionId: mission-normal-absolutepvprategrade-4
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
- missionId: mission-normal-absolutepvprategrade-5
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 500
- missionId: mission-normal-absolutepvprategrade-6
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 600
- missionId: mission-normal-absolutepvprategrade-7
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 600
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 75000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 85000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 90000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 93000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 98000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 103000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 110000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 115000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 123000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 126000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 128000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 130000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 133000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 136000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 139000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 142000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 145000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 148000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 151000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 154000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 157000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutepvprateunitoverallpower-1
  threshold: 160000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 25
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 35
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 45
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 55
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 65
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 75
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 85
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 90
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 95
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 105
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 110
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 115
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardcount-1
  threshold: 120
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 200
- missionId: mission-normal-absolutesupportcardlevel-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-absolutesupportcardlevel-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-absolutesupportcardlevel-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-absolutesupportcardlevel-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-absolutesupportcardlevel-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-absolutesupportcardlevel-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-discord-join-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 700
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 1000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 1500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 2000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 2500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 3000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 3500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 4000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 4500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 5000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 5500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 6000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 6500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementgashadrawcount-1
  threshold: 7000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 250
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 350
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 450
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 600
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 700
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 800
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 900
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 1000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-normal-incrementguilddonationcount-1
  threshold: 1100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 35
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 55
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 85
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 120
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 140
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 160
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 180
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 220
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementidolcardlevellimitrankupdatecount-1
  threshold: 240
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 25
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 90
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 110
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 120
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 130
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 140
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 160
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 170
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 180
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 190
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 210
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 220
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 230
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementIdolcardpotentialrankupdatecount-1
  threshold: 240
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementmeishiexchangecount-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-incrementmeishiexchangecount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-incrementmeishiexchangecount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-incrementmemorygradecount-a-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementmemorygradecount-a-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementmemorygradecount-a-2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-normal-incrementmemorygradecount-a-2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-normal-incrementmemorygradecount-b-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementmemorygradecount-b-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementmemorygradecount-s-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-normal-incrementmemorygradecount-s-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-normal-incrementmemorygradecount-s-2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementmemorygradecount-s-2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementmemorygradecount-s-2
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementmemorygradecount-s-2
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementmemorygradecount-s-2
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementmemorygradecount-ss-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementmemorygradecount-ss-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementmemorygradecount-ss-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementmemorygradecount-ss-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementmemorygradecount-ss-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementmemorygradecount-ss-2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
- missionId: mission-normal-incrementmemorygradecount-ss-2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
- missionId: mission-normal-incrementmemorygradecount-ss-2
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
- missionId: mission-normal-incrementmemorygradecount-ss-2
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
- missionId: mission-normal-incrementmemorygradecount-ss-2
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 450
- missionId: mission-normal-incrementmissionclear-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_FriendFollowLimitCount
    resourceId: ""
    quantity: 5
- missionId: mission-normal-incrementmissionclear-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_FriendFollowLimitCount
    resourceId: ""
    quantity: 5
- missionId: mission-normal-incrementmissionclear-1
  threshold: 90
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_FriendFollowLimitCount
    resourceId: ""
    quantity: 5
- missionId: mission-normal-incrementmissionclear-1
  threshold: 120
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_FriendFollowLimitCount
    resourceId: ""
    quantity: 5
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 75
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 125
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 185
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 225
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 250
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 275
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 350
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 450
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 600
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 700
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 800
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 900
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementmoneyexchangecount-1
  threshold: 1000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2500
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2500
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 75
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 125
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 250
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 600
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 700
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 800
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 900
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementproduceclearcount-1
  threshold: 1000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementproducememorydeckupdatecount-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-incrementproducesupportcarddeckupdatecount-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 5000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 7000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 14000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 18000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 22000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 24000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 26000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 28000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 34000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 36000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 39000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 500
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 42000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 43000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 45000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 47000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 49000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 60000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 80000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 90000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratelivebattlescorecount-01
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 750
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 125
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 175
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 250
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 350
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 600
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 700
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 800
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 900
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 1000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementpvpratewincount-1
  threshold: 1100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-pvp_rate_coin-1
    quantity: 10
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 1000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 200000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 350000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 2000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 500000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 750000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 1000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 1250000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 1500000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 1750000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 3000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 2000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 2500000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 3000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 4000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 3500000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 4000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 4500000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 8000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 5000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 6000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 7000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 8000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-normal-incrementreceivemoney-1
  threshold: 9000000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2500
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2500
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 600
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 700
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 800
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 900
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 1000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 1100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 1200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 1300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 1400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 1600
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 1800
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 2000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 2200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 2400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardenhancecount-1
  threshold: 2600
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2500
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2500
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 50
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 70
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 90
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 2000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 110
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 3500
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 120
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 130
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 140
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 150
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 4000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 160
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 170
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 180
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 190
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 210
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 220
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 230
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 240
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 260
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 280
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 320
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 340
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 360
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 380
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 420
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 440
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 460
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementsupportcardlevellimitrankupdatecount-1
  threshold: 480
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: mission-normal-incrementworktimecount-1
  threshold: 4
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-normal-incrementworktimecount-1
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-normal-incrementworktimecount-1
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 100
- missionId: mission-normal-incrementworktimecount-1
  threshold: 96
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementworktimecount-1
  threshold: 156
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementworktimecount-1
  threshold: 216
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementworktimecount-1
  threshold: 300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-normal-incrementworktimecount-1
  threshold: 420
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-normal-incrementworktimecount-1
  threshold: 600
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-normal-incrementworktimecount-1
  threshold: 900
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementworktimecount-1
  threshold: 1200
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementworktimecount-1
  threshold: 1500
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementworktimecount-1
  threshold: 1800
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementworktimecount-1
  threshold: 2100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 350
- missionId: mission-normal-incrementworktimecount-1
  threshold: 2400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementworktimecount-1
  threshold: 2700
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementworktimecount-1
  threshold: 3000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-incrementworktimecount-1
  threshold: 3300
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 400
- missionId: mission-normal-linkBandaiNamco-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-linkBandaiNamco-2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 1000
- missionId: mission-normal-pcondtionclear-p-cd-amao-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-amao-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-fktn-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-fktn-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-hrnm-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-hrnm-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-hski-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-hski-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-kcna-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-kcna-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-kllj-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-kllj-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-shro-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-shro-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-ssmk-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-ssmk-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-ttmr-1st
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-pcondtionclear-p-cd-ttmr-3rd
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-amao-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-fktn-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-hmsz-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-hrnm-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-hski-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-hume-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-jsna-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-kcna-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-kllj-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-shro-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-ssmk-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-produce-clear-ttmr-c
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-profileupdate-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-normal-view-shop_pack_hmsz-0001
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_hume-0001
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0006
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0007
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0008
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0009
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0010
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0011
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0012
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0013
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_item-0014
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-view-shop_pack_jsna-0001
  threshold: 1
  missionPoint: 0
  rewards: []
- missionId: mission-normal-x-follow-1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: mission-panel-mission-1-1
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produceboostreward_limitovermaterial-1
    quantity: 5
- missionId: mission-panel-mission-1-2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: mission-panel-mission-1-3
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: mission-panel-mission-1-4
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: mission-panel-mission-1-5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produceboostreward_support_card_enhance_point-1
    quantity: 5
- missionId: mission-panel-mission-1-6
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: mission-panel-mission-1-7
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 3000
- missionId: mission-panel-mission-1-8
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 3000
- missionId: mission-panel-mission-1-9
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: mission-panel-mission-2-1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 250
- missionId: mission-panel-mission-2-2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 250
- missionId: mission-panel-mission-2-3
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-panel-mission-2-4
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 30000
- missionId: mission-panel-mission-2-5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 3
- missionId: mission-panel-mission-2-6
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: mission-panel-mission-2-7
  threshold: 1000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-panel-mission-2-8
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: mission-panel-mission-2-9
  threshold: 4000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-release_mission-1-1
  threshold: 50
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-release_mission-1-2
  threshold: 5
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 50
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 50
- missionId: mission-release_mission-1-3
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-release_mission-1-4
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: mission-release_mission-1-5
  threshold: 40
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-release_mission-1-6
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 3000
- missionId: mission-release_mission-2-1
  threshold: 100
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-release_mission-2-2
  threshold: 50000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 150
- missionId: mission-release_mission-2-3
  threshold: 20
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 150
- missionId: mission-release_mission-2-4
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-release_mission-2-5
  threshold: 50
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-release_mission-2-6
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: mission-release_mission-3-1
  threshold: 150
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-release_mission-3-2
  threshold: 75000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 150
- missionId: mission-release_mission-3-3
  threshold: 10
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 150
- missionId: mission-release_mission-3-4
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-release_mission-3-5
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 200
- missionId: mission-release_mission-3-6
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-release_mission-3-7
  threshold: 60
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-release_mission-3-8
  threshold: 7
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 100
- missionId: mission-release_mission-4-1
  threshold: 200
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-release_mission-4-2
  threshold: 100000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 150
- missionId: mission-release_mission-4-3
  threshold: 5
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 150
- missionId: mission-release_mission-4-4
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 5000
- missionId: mission-release_mission-4-5
  threshold: 70
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-release_mission-4-6
  threshold: 5
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: mission-release_mission-5-1
  threshold: 250
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-release_mission-5-2
  threshold: 125000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 200
- missionId: mission-release_mission-5-3
  threshold: 3
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-release_mission-5-4
  threshold: 80
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-release_mission-5-5
  threshold: 4
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-release_mission-6-1
  threshold: 300
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-release_mission-6-2
  threshold: 150000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 200
- missionId: mission-release_mission-6-3
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-release_mission-6-4
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: mission-release_mission-6-5
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-release_mission-6-6
  threshold: 90
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-release_mission-6-7
  threshold: 6
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: mission-release_mission-7-1
  threshold: 400
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 1
- missionId: mission-release_mission-7-2
  threshold: 200000
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 100
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 100
- missionId: mission-release_mission-7-3
  threshold: 1
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 10000
- missionId: mission-release_mission-7-4
  threshold: 100
  missionPoint: 2
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 1000
- missionId: mission-release_mission-7-5
  threshold: 4
  missionPoint: 5
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-3
    quantity: 1
- missionId: mission-story-event-001-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 3000
- missionId: mission-story-event-001-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 5000
- missionId: mission-story-event-001-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-001-mission_2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-001-mission_2
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-001-mission_3
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_3
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_3
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_3
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_3
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 4000
- missionId: mission-story-event-001-mission_3
  threshold: 200000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 4000
- missionId: mission-story-event-001-mission_3
  threshold: 300000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 4000
- missionId: mission-story-event-001-mission_4
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_4
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_4
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_4
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_4
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-001-mission_4
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-001-mission_4
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-001-mission_5
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_5
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_5
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 2000
- missionId: mission-story-event-001-mission_5
  threshold: 45
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 4000
- missionId: mission-story-event-001-mission_5
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 4000
- missionId: mission-story-event-001-mission_5
  threshold: 90
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-001
    quantity: 4000
- missionId: mission-story-event-001-mission_6
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_6
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_6
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_6
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 10
- missionId: mission-story-event-001-mission_6
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-001-mission_6
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-001-mission_6
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-001
    quantity: 20
- missionId: mission-story-event-002-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 3000
- missionId: mission-story-event-002-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 5000
- missionId: mission-story-event-002-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-002-mission_2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-002-mission_2
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-002-mission_3
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_3
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_3
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_3
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_3
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 4000
- missionId: mission-story-event-002-mission_3
  threshold: 200000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 4000
- missionId: mission-story-event-002-mission_3
  threshold: 300000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 4000
- missionId: mission-story-event-002-mission_4
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_4
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_4
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_4
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_4
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-002-mission_4
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-002-mission_4
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-002-mission_5
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_5
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_5
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 2000
- missionId: mission-story-event-002-mission_5
  threshold: 45
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 4000
- missionId: mission-story-event-002-mission_5
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 4000
- missionId: mission-story-event-002-mission_5
  threshold: 90
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-002
    quantity: 4000
- missionId: mission-story-event-002-mission_6
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_6
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_6
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_6
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 10
- missionId: mission-story-event-002-mission_6
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-002-mission_6
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-002-mission_6
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-002
    quantity: 20
- missionId: mission-story-event-003-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 3000
- missionId: mission-story-event-003-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 5000
- missionId: mission-story-event-003-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-003-mission_2
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-003-mission_2
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-003-mission_3
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_3
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_3
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_3
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_3
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 4000
- missionId: mission-story-event-003-mission_3
  threshold: 200000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 4000
- missionId: mission-story-event-003-mission_3
  threshold: 300000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 4000
- missionId: mission-story-event-003-mission_4
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_4
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_4
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_4
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_4
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-003-mission_4
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-003-mission_4
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-003-mission_5
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_5
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_5
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 2000
- missionId: mission-story-event-003-mission_5
  threshold: 45
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 4000
- missionId: mission-story-event-003-mission_5
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 4000
- missionId: mission-story-event-003-mission_5
  threshold: 90
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-003
    quantity: 4000
- missionId: mission-story-event-003-mission_6
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_6
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_6
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_6
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 10
- missionId: mission-story-event-003-mission_6
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-003-mission_6
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-003-mission_6
  threshold: 60
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-003
    quantity: 20
- missionId: mission-story-event-004-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 3000
- missionId: mission-story-event-004-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 5000
- missionId: mission-story-event-004-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-004-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-004-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-004-mission_3
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_3
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_3
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_3
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_3
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 4000
- missionId: mission-story-event-004-mission_3
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 4000
- missionId: mission-story-event-004-mission_3
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 4000
- missionId: mission-story-event-004-mission_4
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_4
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_4
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_4
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_4
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-004-mission_4
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-004-mission_4
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-004-mission_5
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_5
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_5
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_5
  threshold: 21
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 2000
- missionId: mission-story-event-004-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 4000
- missionId: mission-story-event-004-mission_5
  threshold: 36
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 4000
- missionId: mission-story-event-004-mission_5
  threshold: 45
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-004
    quantity: 4000
- missionId: mission-story-event-004-mission_6
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_6
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_6
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_6
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 10
- missionId: mission-story-event-004-mission_6
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-004-mission_6
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-004-mission_6
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-004
    quantity: 20
- missionId: mission-story-event-006-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 3000
- missionId: mission-story-event-006-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 5000
- missionId: mission-story-event-006-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-006-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-006-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-006-mission_3
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_3
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_3
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_3
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_3
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 4000
- missionId: mission-story-event-006-mission_3
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 4000
- missionId: mission-story-event-006-mission_3
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 4000
- missionId: mission-story-event-006-mission_4
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_4
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_4
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_4
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_4
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-006-mission_4
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-006-mission_4
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-006-mission_5
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_5
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_5
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_5
  threshold: 21
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 2000
- missionId: mission-story-event-006-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 4000
- missionId: mission-story-event-006-mission_5
  threshold: 36
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 4000
- missionId: mission-story-event-006-mission_5
  threshold: 45
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-006
    quantity: 4000
- missionId: mission-story-event-006-mission_6
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_6
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_6
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_6
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 10
- missionId: mission-story-event-006-mission_6
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-006-mission_6
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-006-mission_6
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-006
    quantity: 20
- missionId: mission-story-event-013-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_1
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 3000
- missionId: mission-story-event-013-mission_1
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 5000
- missionId: mission-story-event-013-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_2
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_2
  threshold: 7
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-story-event-013-mission_2
  threshold: 12
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-story-event-013-mission_2
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-story-event-013-mission_3
  threshold: 10000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_3
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_3
  threshold: 50000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_3
  threshold: 70000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_3
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 4000
- missionId: mission-story-event-013-mission_3
  threshold: 120000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 4000
- missionId: mission-story-event-013-mission_3
  threshold: 150000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 4000
- missionId: mission-story-event-013-mission_4
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_4
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_4
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_4
  threshold: 14
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_4
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-story-event-013-mission_4
  threshold: 24
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-story-event-013-mission_4
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-story-event-013-mission_5
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_5
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_5
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_5
  threshold: 21
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 2000
- missionId: mission-story-event-013-mission_5
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 4000
- missionId: mission-story-event-013-mission_5
  threshold: 36
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 4000
- missionId: mission-story-event-013-mission_5
  threshold: 45
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_StoryEventPoint
    resourceId: story-event-013
    quantity: 4000
- missionId: mission-story-event-013-mission_6
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_6
  threshold: 6
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_6
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_6
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 10
- missionId: mission-story-event-013-mission_6
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-story-event-013-mission_6
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-story-event-013-mission_6
  threshold: 40
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-story-event-013
    quantity: 20
- missionId: mission-weekly-incrementgashadrawcount-1
  threshold: 10
  missionPoint: 20
  rewards: []
- missionId: mission-weekly-incrementlogincount-1
  threshold: 3
  missionPoint: 20
  rewards: []
- missionId: mission-weekly-incrementmoneyexchangecount-1
  threshold: 5
  missionPoint: 20
  rewards: []
- missionId: mission-weekly-incrementproduceclearcount-1
  threshold: 10
  missionPoint: 40
  rewards: []
- missionId: mission-weekly-incrementpvpratewincount-1
  threshold: 20
  missionPoint: 20
  rewards: []
- missionId: mission-weekly-incrementsupportcardenhancecount-1
  threshold: 10
  missionPoint: 20
  rewards: []
- missionId: mission-weekly-incrementsupportcardenhancecount-1
  threshold: 20
  missionPoint: 20
  rewards: []
- missionId: mission-weekly-incrementworktimecount-1
  threshold: 60
  missionPoint: 20
  rewards: []
- missionId: panel_mission-event-002-mission_1
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-002-mission_11
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 250
- missionId: panel_mission-event-002-mission_12
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 250
- missionId: panel_mission-event-002-mission_13
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: panel_mission-event-002-mission_14
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 30000
- missionId: panel_mission-event-002-mission_15
  threshold: 100000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 300
- missionId: panel_mission-event-002-mission_16
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: panel_mission-event-002-mission_17
  threshold: 700
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: panel_mission-event-002-mission_18
  threshold: 100
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 500
- missionId: panel_mission-event-002-mission_19
  threshold: 2000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: panel_mission-event-002-mission_2
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: panel_mission-event-002-mission_3
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: panel_mission-event-002-mission_4
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 50
- missionId: panel_mission-event-002-mission_5
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-002-mission_6
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: panel_mission-event-002-mission_7
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 3000
- missionId: panel_mission-event-002-mission_8
  threshold: 2
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 3000
- missionId: panel_mission-event-002-mission_9
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: panel_mission-event-003-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-003-mission_2
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-003-mission_3
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-003-mission_4
  threshold: 9
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-003-mission_5
  threshold: 80
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-003-mission_6
  threshold: 900
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-003-mission_7
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-003-mission_8
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-003-mission_9
  threshold: 15
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 150
- missionId: panel_mission-event-004-mission_1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produceboostreward_limitovermaterial-1
    quantity: 5
- missionId: panel_mission-event-004-mission_11
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 250
- missionId: panel_mission-event-004-mission_12
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 250
- missionId: panel_mission-event-004-mission_13
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: panel_mission-event-004-mission_14
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 30000
- missionId: panel_mission-event-004-mission_15
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 3
- missionId: panel_mission-event-004-mission_16
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 5
- missionId: panel_mission-event-004-mission_17
  threshold: 400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: panel_mission-event-004-mission_18
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 5000
- missionId: panel_mission-event-004-mission_19
  threshold: 2000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: panel_mission-event-004-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: panel_mission-event-004-mission_3
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: panel_mission-event-004-mission_4
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: panel_mission-event-004-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produceboostreward_support_card_enhance_point-1
    quantity: 5
- missionId: panel_mission-event-004-mission_6
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: panel_mission-event-004-mission_7
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 3000
- missionId: panel_mission-event-004-mission_8
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 3000
- missionId: panel_mission-event-004-mission_9
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: panel_mission-event-006-mission_1
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produceboostreward_limitovermaterial-1
    quantity: 5
- missionId: panel_mission-event-006-mission_11
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class1
    quantity: 250
- missionId: panel_mission-event-006-mission_12
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-memory_exchange-class2
    quantity: 250
- missionId: panel_mission-event-006-mission_13
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 150
- missionId: panel_mission-event-006-mission_14
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 30000
- missionId: panel_mission-event-006-mission_15
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-staminaregen-1
    quantity: 3
- missionId: panel_mission-event-006-mission_16
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce-reroll-memory-001
    quantity: 5
- missionId: panel_mission-event-006-mission_17
  threshold: 400
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 10000
- missionId: panel_mission-event-006-mission_18
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-event-all-001
    quantity: 10
- missionId: panel_mission-event-006-mission_19
  threshold: 2000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-1
    quantity: 300
- missionId: panel_mission-event-006-mission_2
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: panel_mission-event-006-mission_3
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: panel_mission-event-006-mission_4
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-limitovermaterial-2
    quantity: 30
- missionId: panel_mission-event-006-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produceboostreward_support_card_enhance_point-1
    quantity: 5
- missionId: panel_mission-event-006-mission_6
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-money
    quantity: 20000
- missionId: panel_mission-event-006-mission_7
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-support_card_enhance_point
    quantity: 6000
- missionId: panel_mission-event-006-mission_8
  threshold: 5
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-medal-event-all-001
    quantity: 10
- missionId: panel_mission-event-006-mission_9
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_Item
    resourceId: item-produce_continue-1
    quantity: 5
- missionId: panel_mission-event-niv-mission_1
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-niv-mission_2
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-niv-mission_3
  threshold: 1
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-niv-mission_4
  threshold: 30000
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-niv-mission_5
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-niv-mission_6
  threshold: 3
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-niv-mission_7
  threshold: 10
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-niv-mission_8
  threshold: 20
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100
- missionId: panel_mission-event-niv-mission_9
  threshold: 30
  missionPoint: 0
  rewards:
  - resourceType: ResourceType_JewelTotal
    resourceId: ""
    quantity: 100