- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-00
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 2
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-01
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 3
  subStep: 1
  navigationType: TutorialNavigationType_Unknown
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Start
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 6
  subStep: 1
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    レッスンクリアおめでとう！
    レッスンの目標を達成すると
    報酬を獲得することができますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepPresentReceive
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 6
  subStep: 2
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    この中からひとつ選べるみたいですね
    今回はこちらのスキルカードを
    受け取ってみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepPresentReceive
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 6
  subStep: 3
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepPresentReceive
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 6
  subStep: 4
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: レッスンの基本
  texts:
  - |-
    レッスン目標をクリアすると、
    プロデュースに役立つ報酬を獲得できます。
  assetIds:
  - img_tutorial_produce_01_first-006
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepPresentReceive
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 9
  subStep: 1
  navigationType: TutorialNavigationType_Unknown
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepPresentReceive
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 10
  subStep: 1
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - 体力が少なくなっているようですね
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 10
  subStep: 2
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Lower
  title: ""
  texts:
  - |-
    「休む」を選択して
    アイドルを休ませてあげましょう
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 10
  subStep: 3
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: 体力と休む
  texts:
  - |-
    体力が少なくなったら「休む」で体力を回復して
    試験やレッスンに備えましょう。
  assetIds:
  - img_tutorial_produce_01_first-009
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 10
  subStep: 4
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-04
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 11
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: 試験の基本
  texts:
  - |-
    他のアイドルとスコアを競い合い
    ３位以内に入ると合格できます。
  assetIds:
  - img_tutorial_produce_01_first-010
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 1
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    レッスンと同じスキルカードを使用し、
    スコアを上昇させて順位を競います
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 2
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    こちらで現在のスコアと順位が
    確認できます
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    試験終了時、
    ３位以内だと合格になりますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 4
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    さっそくスキルカードを使用して
    スコアを上げていきましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 5
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    レッスン時より大きく上昇しましたね
    試験ではパラメータに応じて、
    スコアにボーナスがかかります
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 6
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    ボーナスはこちらで確認できます
    ターンごとにボーナスが変わるので
    注意してくださいね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 7
  navigationType: TutorialNavigationType_Character
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    それではあと３ターン
    試験頑張ってください！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 8
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: 試験の基本
  texts:
  - |-
    試験ではターンごとにスコアにボーナスがかかります。
    パラメータが高いほどスコアボーナスの値が大きく
    なります。
  assetIds:
  - img_tutorial_produce_01_first-011
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 12
  subStep: 9
  navigationType: TutorialNavigationType_Unknown
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionExamEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 13
  subStep: 1
  navigationType: TutorialNavigationType_Unknown
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepAuditionEnd
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 14
  subStep: 1
  navigationType: TutorialNavigationType_Unknown
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_CheckBeforeLiveProduceEvaluation
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 15
  subStep: 1
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    ライブ中は最大５０回撮影ができます
    アイドルの姿を記録にのこしましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 16
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ライブについて
  texts:
  - |-
    プロデュース結果とアイドルの成長によって
    出場できるライブが決まります。
  assetIds:
  - img_tutorial_produce_01_first-012
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Result
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 16
  subStep: 2
  navigationType: TutorialNavigationType_Character
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    プロデュースの結果を
    「メモリー」として
    獲得することができますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Result
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 16
  subStep: 3
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: メモリーについて
  texts:
  - |-
    プロデュースの評価に応じて
    メモリーの性能が変化します。
  assetIds:
  - img_tutorial_produce_01_first-013
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Result
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 16
  subStep: 4
  navigationType: TutorialNavigationType_Character
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    「メモリー」にするフォトを選べますよ！
    好きなフォトを選択してみましょう
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Result
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 17
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-05
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_GameStart
  idolCardId: ""
  step: 17
  subStep: 2
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: プロデューサーLv
  texts:
  - |-
    プロデューサーLvが上がると新しいスキルカードや
    Pドリンクなどが解放され、プロデュース中に登場します。
  assetIds:
  - img_tutorial_produce_01_first-014
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 4
  subStep: 1
  navigationType: TutorialNavigationType_Character
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    さぁ、プロデュースがはじまりましたね
    今回の試験は特別に最終だけです
    フォローするので安心してくださいね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 4
  subStep: 2
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: プロデュースの目的
  texts:
  - |-
    試験に合格して、ライブ出場を目指しましょう。
    ライブに出場するためには、
    中間試験と最終試験に合格する必要があります。
  - |-
    試験に合格するためには、
    アイドルのパラメータが重要です。
    試験に向けてパラメータを育成しましょう。
  - |-
    試験ごとに審査基準があります。
    審査基準に合わせてパラメータを育成すると
    試験に合格しやすくなります。
  assetIds:
  - img_tutorial_produce_01_first-001
  - img_tutorial_produce_01_first-002
  - img_tutorial_produce_01_first-003
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 4
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    アイドルのパラメータやアドバイス、
    審査基準はこちらで確認ができます
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 4
  subStep: 4
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    まずはビジュアルレッスンで
    ビジュアルをあげましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-02
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 2
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    クリアまでに必要なパラメータと
    現在のアイドルのパラメータは
    こちらで確認できますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    レッスンでは、決まったターン数
    行動をすることができます
    今回は残り３ターンみたいですね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 4
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    レッスン中は
    こちらにあるスキルカードを選択して
    使用することができますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 5
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - さっそく使用してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 6
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    ビジュアルのパラメータが上昇して
    レッスンクリアに近づきましたね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 7
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    体力が減少していますね
    スキルカードを使用するときには
    体力が必要になることがあります
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 8
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    次はこちらのスキルカードを
    選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 9
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - "{ExamEffectType.ExamBlock}元気が上昇しましたね！\n{ExamEffectType.ExamBlock}元気は体力が減少するとき、\n体力のかわりに優先的に消費されます"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 10
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: レッスンの基本
  texts:
  - ""
  - |-
    スキルカードの使用には、体力が必要になります。
    元気がある時は元気を体力のかわりに消費して
    スキルカードを使用できます。
  assetIds:
  - img_tutorial_produce_01_first-004
  - img_tutorial_produce_01_first-005
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 5
  subStep: 11
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - 最後にもう１枚選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 7
  subStep: 1
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    次はダンスレッスンを
    選択してみましょう
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-03
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 2
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    前回のレッスンで獲得した
    スキルカードが手札にありますね
    選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    スキルカードの効果で
    {ExamEffectType.ExamReview}好印象がつきましたね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 4
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    ターン終了時に
    パラメータが上がりましたね！
    {ExamEffectType.ExamReview}好印象が発動したみたいです
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 5
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    現在の効果はアイコンをタップすると
    確認することができます
    {ExamEffectType.ExamReview}好印象の効果をみてみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 6
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - "{ExamEffectType.ExamReview}好印象はターン終了時に\nパラメータを上昇させる\n効果のようですね"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 7
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    所持しているPアイテムの効果も
    アイコンをタップすると確認できますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 8
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    今持っているPアイテムは
    {ExamEffectType.ExamReview}好印象と相性が良さそうですね
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 9
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    今回は{ExamEffectType.ExamReview}好印象の効果がついている
    スキルカードを選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 10
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - "{ExamEffectType.ExamReview}好印象が増えたおかげで\nパラメータが大きく上がりましたね"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 11
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    さらに、Pアイテムの効果で
    {ExamEffectType.ExamReview}好印象が増えましたね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 12
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: レッスンの基本
  texts:
  - |-
    レッスン中の効果はアイコンをタップすると
    詳細情報の確認ができます。
  - |-
    Pアイテムは条件を満たすと効果を発揮します。
    はじめから持っているPアイテムは
    プロデュースアイドルによって決まります。
  assetIds:
  - img_tutorial_produce_01_first-007
  - img_tutorial_produce_01_first-008
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 13
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    これまで学んだ内容を活かして
    できるだけたくさんパラメータを
    育成できるよう頑張ってみてください！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 14
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - 大丈夫、きみならできますよ！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-fktn-1-000
  step: 8
  subStep: 15
  navigationType: TutorialNavigationType_Unknown
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 4
  subStep: 1
  navigationType: TutorialNavigationType_Character
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    さぁ、プロデュースがはじまりましたね
    今回の試験は特別に最終だけです
    フォローするので安心してくださいね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 4
  subStep: 2
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: プロデュースの目的
  texts:
  - |-
    試験に合格して、ライブ出場を目指しましょう。
    ライブに出場するためには、
    中間試験と最終試験に合格する必要があります。
  - |-
    試験に合格するためには、
    アイドルのパラメータが重要です。
    試験に向けてパラメータを育成しましょう。
  - |-
    試験ごとに審査基準があります。
    審査基準に合わせてパラメータを育成すると
    試験に合格しやすくなります。
  assetIds:
  - img_tutorial_produce_01_first-001
  - img_tutorial_produce_01_first-002
  - img_tutorial_produce_01_first-003
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 4
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    アイドルのパラメータやアドバイス、
    審査基準はこちらで確認ができます
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 4
  subStep: 4
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    まずはダンスレッスンで
    ダンスをあげましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-02
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 2
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    クリアまでに必要なパラメータと
    現在のアイドルのパラメータは
    こちらで確認できますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    レッスンでは、決まったターン数
    行動をすることができます
    今回は残り３ターンみたいですね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 4
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    レッスン中は
    こちらにあるスキルカードを選択して
    使用することができますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 5
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - さっそく使用してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 6
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    ダンスのパラメータが上昇して
    レッスンクリアに近づきましたね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 7
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    体力が減少していますね
    スキルカードを使用するときには
    体力が必要になることがあります
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 8
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    次はこちらのスキルカードを
    選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 9
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - "{ExamEffectType.ExamBlock}元気が上昇しましたね！\n{ExamEffectType.ExamBlock}元気は体力が減少するとき、\n体力のかわりに優先的に消費されます"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 10
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: レッスンの基本
  texts:
  - ""
  - |-
    スキルカードの使用には、体力が必要になります。
    元気がある時は元気を体力のかわりに消費して
    スキルカードを使用できます。
  assetIds:
  - img_tutorial_produce_01_first-004
  - img_tutorial_produce_01_first-005
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 5
  subStep: 11
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - 最後にもう１枚選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 7
  subStep: 1
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - 次はビジュアルレッスンを選択してみましょう
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-03
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 2
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    前回のレッスンで獲得した
    スキルカードが手札にありますね
    選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    スキルカードの効果で
    {ExamEffectType.ExamParameterBuff}好調がつきましたね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 4
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    こちらのPアイテムの効果で
    パラメータがあがりました！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 5
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    現在の効果はアイコンをタップすると
    確認することができます
    {ExamEffectType.ExamParameterBuff}好調の効果をみてみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 6
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - "{ExamEffectType.ExamParameterBuff}好調は上がるパラメータの量を\n増やす効果のようですね"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 7
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    所持しているPアイテムの効果も
    アイコンをタップすると確認できますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 8
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    今持っているPアイテムは
    {ExamEffectType.ExamParameterBuff}好調と相性が良さそうですね
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 9
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - "{ExamEffectType.ExamParameterBuff}好調がついた状態で\nスキルカードを選択してみましょう！"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 10
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - "{ExamEffectType.ExamParameterBuff}好調のおかげで\nパラメータが大きく上がりましたね！"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 11
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: レッスンの基本
  texts:
  - |-
    レッスン中の効果はアイコンをタップすると
    詳細情報の確認ができます。
  - |-
    Pアイテムは条件を満たすと効果を発揮します。
    はじめから持っているPアイテムは
    プロデュースアイドルによって決まります。
  assetIds:
  - img_tutorial_produce_01_first-007
  - img_tutorial_produce_01_first-008
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 12
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    これまで学んだ内容を活かして
    できるだけたくさんパラメータを
    育成できるよう頑張ってみてください！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 13
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - 大丈夫、きみならできますよ！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-hski-1-000
  step: 8
  subStep: 14
  navigationType: TutorialNavigationType_Unknown
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 4
  subStep: 1
  navigationType: TutorialNavigationType_Character
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    さぁ、プロデュースがはじまりましたね
    今回の試験は特別に最終だけです
    フォローするので安心してくださいね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 4
  subStep: 2
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: プロデュースの目的
  texts:
  - |-
    試験に合格して、ライブ出場を目指しましょう。
    ライブに出場するためには、
    中間試験と最終試験に合格する必要があります。
  - |-
    試験に合格するためには、
    アイドルのパラメータが重要です。
    試験に向けてパラメータを育成しましょう。
  - |-
    試験ごとに審査基準があります。
    審査基準に合わせてパラメータを育成すると
    試験に合格しやすくなります。
  assetIds:
  - img_tutorial_produce_01_first-001
  - img_tutorial_produce_01_first-002
  - img_tutorial_produce_01_first-003
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 4
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    アイドルのパラメータやアドバイス、
    審査基準はこちらで確認ができます
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 4
  subStep: 4
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    まずはダンスレッスンで
    ダンスをあげましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-02
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 2
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    クリアまでに必要なパラメータと
    現在のアイドルのパラメータは
    こちらで確認できますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    レッスンでは、決まったターン数
    行動をすることができます
    今回は残り３ターンみたいですね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 4
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    レッスン中は
    こちらにあるスキルカードを選択して
    使用することができますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 5
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - さっそく使用してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 6
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    ダンスのパラメータが上昇して
    レッスンクリアに近づきましたね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 7
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    体力が減少していますね
    スキルカードを使用するときには
    体力が必要になることがあります
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 8
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    次はこちらのスキルカードを
    選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 9
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - "{ExamEffectType.ExamBlock}元気が上昇しましたね！\n{ExamEffectType.ExamBlock}元気は体力が減少するとき、\n体力のかわりに優先的に消費されます"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 10
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: レッスンの基本
  texts:
  - ""
  - |-
    スキルカードの使用には、体力が必要になります。
    元気がある時は元気を体力のかわりに消費して
    スキルカードを使用できます。
  assetIds:
  - img_tutorial_produce_01_first-004
  - img_tutorial_produce_01_first-005
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 5
  subStep: 11
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - 最後にもう１枚選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 7
  subStep: 1
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    次はボーカルレッスンを
    選択してみましょう
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Next
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 1
  navigationType: TutorialNavigationType_Adv
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: adv_tutorial_first_cmmn-03
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 2
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    前回のレッスンで獲得した
    スキルカードが手札にありますね
    選択してみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    スキルカードの効果で
    {ExamEffectType.ExamLessonBuff}集中がつきましたね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 4
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    現在の効果はアイコンをタップすると
    確認することができます
    {ExamEffectType.ExamLessonBuff}集中の効果をみてみましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 5
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - "{ExamEffectType.ExamLessonBuff}集中は上がるパラメータの量を\n増やす効果のようですね"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 6
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    所持しているPアイテムの効果も
    アイコンをタップすると確認できますよ
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 7
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    今持っているPアイテムは
    {ExamEffectType.ExamLessonBuff}集中と相性が良さそうですね
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 8
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - "{ExamEffectType.ExamLessonBuff}集中がついた状態で\nスキルカードを選択してみましょう！"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 9
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - "{ExamEffectType.ExamLessonBuff}集中のおかげで\nパラメータが大きく上がりましたね"
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 10
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    さらに、Pアイテムの効果で
    {ExamEffectType.ExamLessonBuff}集中が増えましたね！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 11
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: レッスンの基本
  texts:
  - |-
    レッスン中の効果はアイコンをタップすると
    詳細情報の確認ができます。
  - |-
    Pアイテムは条件を満たすと効果を発揮します。
    はじめから持っているPアイテムは
    プロデュースアイドルによって決まります。
  assetIds:
  - img_tutorial_produce_01_first-007
  - img_tutorial_produce_01_first-008
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 12
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    これまで学んだ内容を活かして
    できるだけたくさんパラメータを
    育成できるよう頑張ってみてください！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 13
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - 大丈夫、きみならできますよ！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_GameStart
  idolCardId: i_card-ttmr-1-000
  step: 8
  subStep: 14
  navigationType: TutorialNavigationType_Unknown
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_StepLessonEnd
- tutorialType: TutorialType_MainTask
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    課題をクリアすることで新しい機能や
    初星コミュ解放することができます。
    また、プロデューサーExpなどの報酬が
    獲得できます。
  assetIds:
  - img_tutorial_maintask_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_Work
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    アイドルをお仕事に派遣すると
    さまざまな報酬を獲得できます。
  - |-
    お仕事は大成功になることがあり、
    大成功になると獲得できる報酬量が増加します。
    お仕事開始時に好調のアイドルは
    必ず大成功になります。
  assetIds:
  - img_tutorial_work_001
  - img_tutorial_work_002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_IdolCard
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    プロデュースアイドルを才能開花すると、
    初期Pアイテムやレッスンボーナスが強化されます。
  assetIds:
  - img_tutorial_idolcard_002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_IdolCardSkin
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_another-idol_001
  - img_tutorial_another-idol_002
  - img_tutorial_another-idol_003
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PvpRate
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    全国のプロデューサーと対戦することができます。
    コンテストでは、プロデュースで獲得した
    メモリーに紐づくパラメータ、スキルカード、
    Pアイテムをつかって対戦ができます。
  - |-
    ステージごとにパフォーマンス対決を行い、
    獲得したスコアで勝敗を競います。
    ステージの勝利数が多いプロデューサーが
    勝利となります。
  - |-
    コンテストにはグレードがあります。
    ランキング上位になると
    より高いグレードに昇格できます。
    グレードが高くなると報酬が豪華になります。
  - |-
    コンテストメダルを集めると
    アイテム交換所で
    豪華な報酬と交換することができます。
  assetIds:
  - img_tutorial_pvprate_001
  - img_tutorial_pvprate_002
  - img_tutorial_pvprate_003
  - img_tutorial_pvprate_004
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PvpRate
  idolCardId: ""
  step: 1
  subStep: 2
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Lower
  title: ""
  texts:
  - |-
    コンテストに参加するために
    ユニットを編成しましょう
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PvpRate
  idolCardId: ""
  step: 1
  subStep: 3
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    おまかせを使うと
    まとめて編成ができます
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PvpRate
  idolCardId: ""
  step: 1
  subStep: 4
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    おまかせを使うと
    まとめて編成ができます
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PvpRate
  idolCardId: ""
  step: 1
  subStep: 5
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PvpRate
  idolCardId: ""
  step: 1
  subStep: 6
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    メモリーはステージごとに
    編成可能なプランが決まっています。
  - |-
    メモリー編成にはメインとサブがあります。
    メインとサブには同じアイドルのメモリーを
    編成できます。
  - |-
    メインに編成するとメモリーが持つ性能すべてが
    パフォーマンス対決に反映されます。
    サブに編成すると性能の一部が反映されます。
  assetIds:
  - img_tutorial_pvprate_005
  - img_tutorial_pvprate_006
  - img_tutorial_pvprate_007
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PvpRate
  idolCardId: ""
  step: 1
  subStep: 7
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - 戻ってコンテストに参加しましょう
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_CoinGasha
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    コインガシャではプロデュースなどで獲得できる
    コインでガシャを引くことができます。
    サポートカードや特訓アイテムなどが獲得できます。
  assetIds:
  - img_tutorial_normalgasha_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_MoneyReceive
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    時間経過でマニーが貯まり、受け取ることができます。
    プロデューサーLvに応じて、獲得量と保管上限が増加します。
  assetIds:
  - img_tutorial_moneyreceive_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_MissionPass
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    ミッションパスではミッションパスPtが一定以上
    に到達する度に報酬を獲得できます。
    ミッションパスPtはデイリーミッションと
    ウィークリーミッション達成で獲得できます。
  assetIds:
  - img_tutorial_missionpass_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_Profile
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    プロフィールでは他のプロデューサーがレンタルできる
    サポートカードとメモリーが設定できます。
    レンタルされた回数に応じてフレンドコインを獲得できます。
  assetIds:
  - img_tutorial_profile_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_Meishi
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    自分だけのオリジナル名刺を作成して、名刺の画像をSNSで共有することができます。プロフィールだけでなく、担当アイドルやサポートカード、フォトといった画像をカスタムして表示させることが可能です。
    ※DMM GAMES版ではSNS共有をご利用いただけません
  assetIds:
  - img_tutorial_meishi_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_Friend
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - フレンドではフォローやフォロワーのプロデューサーを確認できます。フォローしたフレンドのサポートカードやメモリーはプロデュースの時にレンタルできます。
  assetIds:
  - img_tutorial_friend_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_Achievement
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - アチーブメントは様々な条件を満たすと獲得できます。プロデューサーLv上昇のための経験値など様々な報酬を獲得できるので積極的にアチーブメントを集めましょう。
  - アチーブメントを獲得した時に入手できる星が一定数に達するとマスターアチーブメントを獲得できます。マスターアチーブメントは入手した星の数に応じてランクアップし、報酬を獲得できます。
  - True EndアチーブメントはTrue Endに到達すると獲得できます。True Endアチーブメントを獲得すると、アイドルのパラメータが上昇します。
  assetIds:
  - img_tutorial_achievement_001
  - img_tutorial_achievement_002
  - img_tutorial_achievement_003
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_IdolCardLevelLimitRankUpdate
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    プロデュースアイドルを特訓すると、
    初期スキルカードやパラメータが強化され、
    プロデュースが有利になります。また、
    プロデュースアイドルのイメージが解放されます。
  assetIds:
  - img_tutorial_idolcard_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_SupportCardLevelUpdate
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    サポートカードのLvを上げると、
    サポートアビリティやサポートイベントが解放されます。
    また、サポートアビリティは
    Lvによって強化されます。
  - |-
    サポートカードを上限解放をすると、
    最大Lvが上昇します。
  assetIds:
  - img_tutorial_supportcard_001
  - img_tutorial_supportcard_002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_Tower
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_tower_001_01
  - img_tutorial_tower_001_02
  - img_tutorial_tower_001_03
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_Guild
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - サークルでは全国のプロデューサーとサークルを組んで交流することができます。
  - サークルではサークルメンバーに対して特訓アイテムを募集することができ、寄付されたアイテムを受け取ることができます。またアイテムを寄付するとフレンドコインを獲得できます。
  assetIds:
  - img_tutorial_guildjoin_001
  - img_tutorial_guildestablish_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_TutorialReceiveIdolCard
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_tutorialreceiveidolcard_001
  - img_tutorial_tutorialreceiveidolcard_002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PhotoTop
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_photo_top-001_01
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PhotoIdol
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_photo_idol-top-001_01
  - img_tutorial_photo_idol-top-001_02
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_PhotoPrepare
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_photo_prepare-001_01
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_Photo
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_photo_photo-001_01
  - img_tutorial_photo_photo-001_02
  - img_tutorial_photo_photo-001_03
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_MeishiEditCustom
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - カスタム名刺は【名刺 > 新規作成 > カスタム名刺】より作成できます
  - テンプレート名刺では背景のみに指定可能でしたが、カスタム名刺では複数指定可能です
  - |-
    動く名刺はPライブを動画で撮影し、撮影した動画の指定部分を名刺として設定できます
    動画は指定の秒数内で切り取りして設定することができます
  - |-
    カスタム名刺ではさまざまな編集ができます
    自分だけの名刺を作りましょう！
    操作方法はカスタム名刺左上のアイコンから確認できます
  - |-
    カスタム名刺では現在使用している内容をレイヤーウィンドウで確認できます
    レイヤーは最大で100個まで使用することができます
  - |-
    SNSでシェアすると、他のプロデューサーがSNSから
    学園アイドルマスター内のあなたのプロフィールに直接移動することができます
    ※DMM GAMES版ではご利用いただけません
  assetIds:
  - img_tutorial_meishi_custom-01
  - img_tutorial_meishi_custom-02
  - img_tutorial_meishi_custom-03
  - img_tutorial_meishi_custom-04
  - img_tutorial_meishi_custom-05
  - img_tutorial_meishi_custom-06
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_MeishiEditCustomManual
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tips_meishi_custom-01
  - img_tips_meishi_custom-02
  - img_tips_meishi_custom-03
  - img_tips_meishi_custom-04
  - img_tips_meishi_custom-05
  - img_tips_meishi_custom-06
  - img_tips_meishi_custom-07
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_IdolCardSkinUnit
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_unit-idol_001
  - img_tutorial_unit-idol_002
  - img_tutorial_unit-idol_003
  - img_tutorial_unit-idol_004
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_DearnessTop
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_dearness_004
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_DearnessPoint
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_dearness_001
  - img_tutorial_dearness_002
  - img_tutorial_dearness_003
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceIdolCardSelect
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - ""
  - |-
    プロデュースアイドルごとに「プラン」が決まっています。
    プランごとにプロデュース中に登場するスキルカードやPアイテムの種類が異なります。
  assetIds:
  - img_tutorial_produce-idolcardselect_001
  - img_tutorial_produce-idolcardselect_002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceSupportCardSelect
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    サポートカードはプロデュースで編成できるカードで
    す。編成すると様々な効果が発動し、プロデュースを
    サポートします。
  - |-
    サポートカードにはタイプがあり、
    タイプによって編成時に上がるパラメータや
    発動する効果が変わります。
  assetIds:
  - img_tutorial_produce-supportcardselect_001
  - img_tutorial_produce-supportcardselect_002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceMemorySelect
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    メモリーは４枚のうち１枚まで
    フレンドや他のプロデューサーのメモリーを
    レンタルすることができます。
    １日にできるレンタル回数には限りがあります。
  assetIds:
  - img_tutorial_produce-memoryselect_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceSchedule
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - レッスンはボーカル、ダンス、ビジュアルがあり、それぞれのパラメータを上げることができます。レッスンの目標を達成すると報酬としてスキルカードやPドリンクが獲得できます。
  - プロデュースでは様々なスケジュールを選択することができます。状況に応じて適切なスケジュールを選びましょう。
  - 試験の前に行う特別なレッスンです。レッスンの目標を達成すると追い込みボーナスが発生し、ボーカル、ダンス、ビジュアルのパラメータを均等に成長させることができます。
  - 試験前には、体力が最大体力に応じて自動で回復します。
  assetIds:
  - img_tutorial_produce-schedule_001
  - img_tutorial_produce-schedule_002
  - img_tutorial_produce-schedule_003
  - img_tutorial_produce-schedule_004
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceEvent
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    授業とおでかけでは
    選択肢に応じて効果が変わります。
    消費する内容と発動する効果を確認して
    状況に応じた選択をしましょう。
  assetIds:
  - img_tutorial_produce-event_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProducePresentStep
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    活動支給では、スキルカード、Pポイント、Pドリンクを
    獲得することができます。
  assetIds:
  - img_tutorial_produce-presentstep_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceShopStep
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    相談ではPポイントを使って、
    スキルカードやPドリンクと交換できます。
    交換できるスキルカードやPドリンクは
    相談のたび毎回変わります。
  - |-
    Pドリンクを使うとパラメータの上昇や
    体力回復などの効果を発揮します。
    Pドリンクはレッスンや試験中のみ使用できます。
  assetIds:
  - img_tutorial_produce-shopstep_001
  - img_tutorial_produce-shopstep_002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceExamGimmick
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    指定ターンの時に条件を満たしていると自動的に
    発動する効果です。レッスン中に表示されている
    アイコンをタップすると発動条件を確認できます。
  assetIds:
  - img_tutorial_produce-examgimmick_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceExamGimmick
  idolCardId: ""
  step: 1
  subStep: 2
  navigationType: TutorialNavigationType_Arrow
  navigationPositionType: TutorialNavigationPositionType_Upper
  title: ""
  texts:
  - |-
    応援やトラブルの内容は
    こちらをタップすると確認できます
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceExamGimmick
  idolCardId: ""
  step: 1
  subStep: 3
  navigationType: TutorialNavigationType_Focus
  navigationPositionType: TutorialNavigationPositionType_Lower
  title: ""
  texts:
  - |-
    効果内容と発動条件を確認して
    しっかり対策しましょう！
  assetIds: []
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceExamPerfect
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    レッスン目標クリア後にパラメータを上限まで
    上げるとパーフェクトレッスンになります。
    パーフェクトレッスンになるとレッスンが終了し
    残ったターンに応じて体力が回復します。
  assetIds:
  - img_tutorial_produce-examperfect_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceBeforeLiveEvaluation
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    True Endは特定のアチーブメントを獲得した状態で
    最終試験１位になるとみられる特別なエンディングです。
  - |-
    True Endに必要なアチーブメントは
    「True End条件」をタップすると確認することができます。
  assetIds:
  - img_tutorial_produce-beforeliveevaluation_001
  - img_tutorial_produce-beforeliveevaluation_002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceDifficultySelect
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    難易度「プロ」が解放されました。
    難易度「プロ」は難易度が高いかわりに
    アイドルのパラメータが上がりやすくなります。
  assetIds:
  - img_tutorial_produce-difficultyselect_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceCardUpgrade
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    Pポイントを使ってスキルカード強化ができます。
    強化をするとスキルカードの効果が上がります。
    １回の相談につき１枚まで強化でき、強化をする度
    必要なPポイントが増えていきます。
  assetIds:
  - img_tutorial_produce-cardupgrade_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceCardDelete
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    Pポイントを使ってスキルカード削除ができます。
    削除をすると所持スキルカードからなくなります。
    １回の相談につき１枚まで削除でき、削除をする度
    必要なPポイントは増えていきます。
  assetIds:
  - img_tutorial_produce-carddelete_001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceChallenge
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts:
  - |-
    難易度「マスター」が解放されました。
    「プロ」よりも難易度が高いかわりにアイドルのパラメータがさらに上がりやすくなります。
    親愛度10を達成したアイドルでのみ挑戦可能です。
  - |-
    チャレンジPアイテムは、難易度「マスター」のプロデュース開始前に選択可能なPアイテムです。
    チャレンジPアイテムの効果により、レッスン・SPレッスンのPERFECT上限を上げることができます。
  - チャレンジPアイテムは、アイドルごとに特定の条件を満たすことで最大3つ解放され、選択することができます。
  assetIds:
  - img_tutorial_produce-difficultyselect_002
  - img_tutorial_produce-difficultyselect_003
  - img_tutorial_produce-difficultyselect_004
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceNextIdolAuditionTop
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_produce_02-001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceNextIdolAuditionSchedule
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_produce_02-002
  - img_tutorial_produce_02-003
  - img_tutorial_produce_02-004
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceNextIdolAuditionStepAuditionSelect
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_produce_02_audition-001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceNextIdolAuditionStepAuditionStart
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_produce_02_audition-002
  - img_tutorial_produce_02_audition-003
  - img_tutorial_produce_02_audition-004
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceNextIdolAuditionResult
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_produce_02-005
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceStepBusiness
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_produce_02_business-001
  - img_tutorial_produce_02_business-002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceStepCustomize
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_produce_02_customize-001
  - img_tutorial_produce_02_customize-002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceStepFanPresent
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_produce_02_present-001
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_MissionPanel
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_event_panel_event-panel-mission-001-01
  - img_tutorial_event_panel_event-panel-mission-001-02
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceHighScore
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: 十王邦夫のアイドル強化月間〜星々のきらめき〜
  texts: []
  assetIds:
  - img_tutorial_event_high-score-rush-event_002-01
  - img_tutorial_event_high-score-rush-event_002-02
  - img_tutorial_event_high-score-rush-event_002-03
  - img_tutorial_event_high-score-rush-event_002-04
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_StoryEvent
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_event_story_event-story-014-01
  - img_tutorial_event_story_event-story-014-02
  - img_tutorial_event_story_event-story-014-03
  - img_tutorial_event_story_event-story-014-04
  - img_tutorial_event_story_event-story-014-05
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_StoryEventMainStroy
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_event_main-story_event-main-story-001-01
  - img_tutorial_event_main-story_event-main-story-001-02
  - img_tutorial_event_main-story_event-main-story-001-03
  - img_tutorial_event_main-story_event-main-story-001-04
  - img_tutorial_event_main-story_event-main-story-009-05
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_StoryEventBoxGasha
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_event_box-gasha-event-015_001
  - img_tutorial_event_box-gasha-event-015_002
  - img_tutorial_event_box-gasha-event-015_003
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_StoryEventGuildMission
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: ""
  texts: []
  assetIds:
  - img_tutorial_event_circle-mission_circle-mission-001
  - img_tutorial_event_circle-mission_circle-mission-002
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_GvgRaid
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: 地方行脚
  texts: []
  assetIds:
  - img_tutorial_event_circle-battle-event_circle-battle-event-001-01
  - img_tutorial_event_circle-battle-event_circle-battle-event-001-02
  - img_tutorial_event_circle-battle-event_circle-battle-event-001-03
  - img_tutorial_event_circle-battle-event_circle-battle-event-001-04
  - img_tutorial_event_circle-battle-event_circle-battle-event-001-05
  - img_tutorial_event_circle-battle-event_circle-battle-event-001-06
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceNextIdolAuditionMaster
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: N.I.Aマスター
  texts:
  - |-
    「N.I.A」の難易度「マスター」が解放されました。
    同時に「N.I.Aマスターランキング」も解放されます。
    「プロ」よりも難易度が高いかわりにパラメータがさらにあがりやすくなり、ファン投票数も集めやすくなります。
    親愛度20を達成したアイドルでのみ挑戦可能です。
  - |-
    チャレンジPアイテムは、難易度「マスター」のプロデュース開始前に選択可能なPアイテムです。
    チャレンジPアイテムの効果により、オーディションクリア時にオーディションボーナスによって獲得するパラメータの量を増加させることができます。
  assetIds:
  - img_tutorial_produce_02_difficultyselect-01
  - img_tutorial_produce_02_difficultyselect-02
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown
- tutorialType: TutorialType_ProduceNextIdolAuditionMasterRanking
  idolCardId: ""
  step: 1
  subStep: 1
  navigationType: TutorialNavigationType_Tips
  navigationPositionType: TutorialNavigationPositionType_Unknown
  title: N.I.Aマスターランキング
  texts: []
  assetIds:
  - img_tutorial_produce_02_master-ranking-01
  - img_tutorial_produce_02_master-ranking-02
  - img_tutorial_produce_02_master-ranking-04
  advAssetId: ""
  tutorialProduceCommandType: TutorialProduceCommandType_Unknown