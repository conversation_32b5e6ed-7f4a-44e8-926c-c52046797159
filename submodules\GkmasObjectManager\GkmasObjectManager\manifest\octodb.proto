syntax = "proto3";

message Database {
    int32 revision = 1;
    repeated Data assetBundleList = 2;
    repeated string tagname = 3;
    repeated Data resourceList = 4;
    string urlFormat = 5;
}

message Data {
    int32 id = 1;
    string filepath = 2;
    string name = 3;
    int32 size = 4;
    uint32 crc = 5;
    int32 priority = 6;
    repeated int32 tagid = 7;
    repeated int32 dependencies = 8;
    State state = 9;
    string md5 = 10;
    string objectName = 11;
    uint64 generation = 12;
    int32 uploadVersionId = 13;

    enum State {
        NONE = 0;
        ADD = 1;
        UPDATE = 2;
        LATEST = 3;
        DELETE = 4;
    }
}
