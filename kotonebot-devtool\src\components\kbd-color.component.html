<style id="kbd-color-style">
    .kbd-color-wrapper {
        position: relative;
    }

    .kbd-color-square {
        width: var(--size, 24px);
        height: var(--size, 24px);
        border: 2px solid #888;
        cursor: pointer;
    }

    .kbd-color-tooltip {
        position: absolute;
        padding: 8px;
        border-radius: 4px;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        font-size: 12px;
        z-index: 1000;
        white-space: nowrap;
        top: calc(100% + 5px);
        left: 0;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.15s ease, visibility 0s linear 0.3s;
    }

    .kbd-color-tooltip.show {
        opacity: 1;
        visibility: visible;
        transition: opacity 0.2s ease, visibility 0s linear 0s;
    }

    .kbd-color-tooltip.hide {
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.15s ease, visibility 0s linear 0.15s;
    }

    @media (prefers-color-scheme: light) {
        .kbd-color-tooltip {
            background: #f3f3f3;
            border: 1px solid #d4d4d4;
            color: #333;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .kbd-color-copy-btn {
            background: #007acc;
            color: white;
        }

        .kbd-color-copy-btn:hover {
            background: #005999;
        }
    }

    @media (prefers-color-scheme: dark) {
        .kbd-color-tooltip {
            background: #252526;
            border: 1px solid #454545;
            color: #e0e0e0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .kbd-color-copy-btn {
            background: #0e639c;
            color: white;
        }

        .kbd-color-copy-btn:hover {
            background: #1177bb;
        }
    }

    .kbd-color-tooltip-row {
        display: flex;
        align-items: center;
        margin: 4px 0;
    }

    .kbd-color-copy-btn {
        margin-left: 8px;
        padding: 2px 6px;
        border: none;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
    }
</style>

<template id="kbd-color-template">
    <div class="kbd-color-wrapper">
        <div class="kbd-color-square"></div>
        <div class="kbd-color-tooltip">
            <div class="kbd-color-tooltip-row">
                <span class="kbd-color-hex-value"></span>
                <button class="kbd-color-copy-btn kbd-color-copy-hex">复制</button>
            </div>
            <div class="kbd-color-tooltip-row">
                <span class="kbd-color-rgb-value"></span>
                <button class="kbd-color-copy-btn kbd-color-copy-rgb">复制</button>
            </div>
        </div>
    </div>
</template>

<script>
    console.log(111);
/**
 * @class KbdColor
 * @extends HTMLElement
 * @description 自定义Web组件，用于显示颜色预览和颜色值。支持十六进制和RGB格式的颜色显示及复制功能。
 * @property {HTMLElement} colorSquare - 显示颜色的方块元素
 * @property {HTMLElement} tooltip - 显示颜色值的提示框元素
 * @property {HTMLElement} hexSpan - 显示十六进制颜色值的元素
 * @property {HTMLElement} rgbSpan - 显示RGB颜色值的元素
 * @property {number} hideTimeout - 控制tooltip隐藏的定时器ID
 */
class KbdColor extends HTMLElement {
    /**
     * @constructor
     * @description 初始化KbdColor组件，设置DOM结构和事件监听
     */
    constructor() {
        super();
        
        // 创建 shadow root
        const shadow = this.attachShadow({ mode: 'open' });
        
        // 克隆样式
        const style = document.getElementById('kbd-color-style').cloneNode(true);
        
        // 克隆模板内容
        const template = document.getElementById('kbd-color-template');
        const content = template.content.cloneNode(true);

        // 将样式和内容添加到 shadow root
        shadow.appendChild(style);
        shadow.appendChild(content);

        // 获取 shadow DOM 中的元素
        this.colorSquare = shadow.querySelector('.kbd-color-square');
        this.tooltip = shadow.querySelector('.kbd-color-tooltip');
        this.hexSpan = shadow.querySelector('.kbd-color-hex-value');
        this.rgbSpan = shadow.querySelector('.kbd-color-rgb-value');

        this.hideTimeout = null;
        this.setupEvents();

        this.updateColor(this.getAttribute('color') || '#000000');
        this.updateSize(this.getAttribute('size') || '24px');
    }

    /**
     * @private
     * @description 设置组件的事件监听器，包括鼠标悬停和复制按钮的点击事件
     */
    setupEvents() {
        const showTooltip = () => {
            clearTimeout(this.hideTimeout);
            this.tooltip.classList.add('show');
        };

        const hideTooltip = () => {
            this.hideTimeout = setTimeout(() => {
                this.tooltip.classList.remove('show');
            }, 100);
        };

        this.colorSquare.addEventListener('mouseenter', showTooltip);
        this.colorSquare.addEventListener('mouseleave', hideTooltip);
        this.tooltip.addEventListener('mouseenter', showTooltip);
        this.tooltip.addEventListener('mouseleave', hideTooltip);

        this.shadowRoot.querySelector('.kbd-color-copy-hex').addEventListener('click', (e) => {
            navigator.clipboard.writeText(this.getAttribute('color'));
            const btn = e.target;
            const originalText = btn.textContent;
            btn.textContent = '已复制';
            setTimeout(() => {
                btn.textContent = originalText;
            }, 1000);
        });

        this.shadowRoot.querySelector('.kbd-color-copy-rgb').addEventListener('click', (e) => {
            navigator.clipboard.writeText(this.rgbSpan.textContent);
            const btn = e.target;
            const originalText = btn.textContent;
            btn.textContent = '已复制';
            setTimeout(() => {
                btn.textContent = originalText;
            }, 1000);
        });
    }

    /**
     * @static
     * @returns {string[]} 返回需要观察的属性列表
     */
    static get observedAttributes() {
        return ['color', 'size'];
    }

    /**
     * @param {string} name - 属性名称
     * @param {string} oldValue - 属性的旧值
     * @param {string} newValue - 属性的新值
     * @description 当观察的属性发生变化时调用的回调函数
     */
    attributeChangedCallback(name, oldValue, newValue) {
        if (name === 'color') {
            this.updateColor(newValue);
        } else if (name === 'size') {
            this.updateSize(newValue);
        }
    }

    /**
     * @param {string} size - 要设置的大小值，可以是数字或带单位的字符串
     * @description 更新颜色方块的大小
     */
    updateSize(size) {
        if (!size) return;
        const sizeValue = /^\d+$/.test(size) ? `${size}px` : size;
        this.style.setProperty('--size', sizeValue);
    }

    /**
     * @param {string} color - 要设置的颜色值，格式为十六进制（如 #FF0000）
     * @description 更新颜色方块的颜色并更新显示的颜色值
     */
    updateColor(color) {
        if (!color) return;
        
        this.colorSquare.style.backgroundColor = color;
        this.hexSpan.textContent = color.toUpperCase();
        
        // Convert hex to RGB
        const r = parseInt(color.slice(1, 3), 16);
        const g = parseInt(color.slice(3, 5), 16);
        const b = parseInt(color.slice(5, 7), 16);
        this.rgbSpan.textContent = `rgb(${r}, ${g}, ${b})`;
    }
}

customElements.define('kbd-color', KbdColor);
console.log('KbdColor loaded', KbdColor);

// 示例用法
/*
$(document).ready(function() {
    $('body').append(`
        <h3>颜色预览示例：</h3>
        <p>默认大小 (24px)：</p>
        <kbd-color color="#FF5733"></kbd-color>
        <kbd-color color="#33FF57"></kbd-color>
        <kbd-color color="#3357FF"></kbd-color>
        
        <p>自定义大小：</p>
        <kbd-color color="#FF5733" size="16px"></kbd-color>
        <kbd-color color="#33FF57" size="32"></kbd-color>
        <kbd-color color="#3357FF" size="48px"></kbd-color>
    `);
});
*/
</script>
