- type: ProduceExamEffectType_ExamLesson
  name: パラメータ
  produceDescriptionSwapId: Swap_Label_ExamLesson
  produceDescriptionLabelId: Label_ExamLesson
  examProduceDescriptionLabelId: Label_ExamLesson
  mainBuffMinThresholds: []
  noIcon: true
  noReference: true
- type: ProduceExamEffectType_ExamParameterBuff
  name: 好調
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamParameterBuff
  examProduceDescriptionLabelId: Label_ExamParameterBuff
  mainBuffMinThresholds:
  - 3
  - 5
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamBlock
  name: 元気
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamBlock
  examProduceDescriptionLabelId: Label_ExamBlock
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamCardDraw
  name: スキルカードを引く
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamCardDraw
  examProduceDescriptionLabelId: Label_ExamCardDraw
  mainBuffMinThresholds: []
  noIcon: false
  noReference: true
- type: ProduceExamEffectType_ExamStaminaConsumptionDown
  name: 消費体力減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaConsumptionDown
  examProduceDescriptionLabelId: Label_ExamStaminaConsumptionDown
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamCardCreateId
  name: 生成
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamCardCreateId
  examProduceDescriptionLabelId: Label_ExamCardCreateId
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaReduceFix
  name: 体力消費
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaReduceFix
  examProduceDescriptionLabelId: Label_ExamStaminaReduceFix
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonBuff
  name: 集中
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamLessonBuff_Produce
  examProduceDescriptionLabelId: Label_ExamLessonBuff_Exam
  mainBuffMinThresholds:
  - 5
  - 10
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamCardUpgrade
  name: レッスン中強化
  produceDescriptionSwapId: Swap_Label_ExamCardUpgrade
  produceDescriptionLabelId: Label_ExamCardUpgrade
  examProduceDescriptionLabelId: Label_ExamCardUpgrade
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamPlayableValueAdd
  name: スキルカード使用数追加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamPlayableValueAdd_Produce
  examProduceDescriptionLabelId: Label_ExamPlayableValueAdd_Exam
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonBuffMultiple
  name: 集中効果一時増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamLessonBuffMultiple
  examProduceDescriptionLabelId: Label_ExamLessonBuffMultiple
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamCardStaminaConsumptionChange
  name: 消費体力変化
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamCardStaminaConsumptionChange
  examProduceDescriptionLabelId: Label_ExamCardStaminaConsumptionChange
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamBlockRestriction
  name: 元気増加無効
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamBlockRestriction
  examProduceDescriptionLabelId: Label_ExamBlockRestriction
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStatusEnchant
  name: 持続効果
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStatusEnchant
  examProduceDescriptionLabelId: Label_ExamStatusEnchant
  mainBuffMinThresholds: []
  noIcon: false
  noReference: true
- type: ProduceExamEffectType_ExamCardStaminaConsumptionDownSpecify
  name: 消費体力低下
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamCardStaminaConsumptionDownSpecify
  examProduceDescriptionLabelId: Label_ExamCardStaminaConsumptionDownSpecify
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaDamage
  name: 体力減少
  produceDescriptionSwapId: Swap_Label_ExamStaminaDamage
  produceDescriptionLabelId: Label_ExamStaminaDamage
  examProduceDescriptionLabelId: Label_ExamStaminaDamage
  mainBuffMinThresholds: []
  noIcon: false
  noReference: true
- type: ProduceExamEffectType_ExamStaminaRecoverFix
  name: 体力回復
  produceDescriptionSwapId: Swap_Label_ExamStaminaRecoverFix
  produceDescriptionLabelId: Label_ExamStaminaRecoverFix
  examProduceDescriptionLabelId: Label_ExamStaminaRecoverFix
  mainBuffMinThresholds: []
  noIcon: false
  noReference: true
- type: ProduceExamEffectType_ExamLessonFix
  name: 固定パラメータ
  produceDescriptionSwapId: Swap_Label_ExamLessonFix
  produceDescriptionLabelId: Label_ExamLessonFix
  examProduceDescriptionLabelId: Label_ExamLessonFix
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamCardDuplicate
  name: 複製
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamCardDuplicate
  examProduceDescriptionLabelId: Label_ExamCardDuplicate
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamReview
  name: 好印象
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamReview_Produce
  examProduceDescriptionLabelId: Label_ExamReview_Exam
  mainBuffMinThresholds:
  - 5
  - 10
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamCardSearchEffectPlayCountBuff
  name: スキルカード追加発動
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamCardSearchEffectPlayCountBuff_Produce
  examProduceDescriptionLabelId: Label_ExamCardSearchEffectPlayCountBuff
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonValueMultiple
  name: パラメータ上昇量増加
  produceDescriptionSwapId: Swap_Label_ExamLessonValueMultiple
  produceDescriptionLabelId: Label_ExamLessonValueMultiple
  examProduceDescriptionLabelId: Label_ExamLessonValueMultiple
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamCardPlayAggressive
  name: やる気
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamCardPlayAggressive_Produce
  examProduceDescriptionLabelId: Label_ExamCardPlayAggressive_Exam
  mainBuffMinThresholds:
  - 5
  - 10
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamConcentration
  name: 強気
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamConcentration_Produce
  examProduceDescriptionLabelId: Label_ExamConcentration_Exam
  mainBuffMinThresholds:
  - 1
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamPreservation
  name: 温存
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamPreservation_Produce
  examProduceDescriptionLabelId: Label_ExamPreservation_Exam
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamFullPower
  name: 全力
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamFullPower
  examProduceDescriptionLabelId: Label_ExamFullPower
  mainBuffMinThresholds:
  - 1
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStanceReset
  name: 指針解除
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStanceReset
  examProduceDescriptionLabelId: Label_ExamStanceReset
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamFullPowerPoint
  name: 全力値
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamFullPowerPoint
  examProduceDescriptionLabelId: Label_ExamFullPowerPoint
  mainBuffMinThresholds:
  - 5
  - 10
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamFullPowerPointReduce
  name: 全力値減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamFullPowerPointReduce
  examProduceDescriptionLabelId: Label_ExamFullPowerPointReduce
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamSearchPlayCardStaminaConsumptionChange
  name: 消費体力変化
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamSearchPlayCardStaminaConsumptionChange
  examProduceDescriptionLabelId: Label_ExamSearchPlayCardStaminaConsumptionChange
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamUplifting
  name: 高揚
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamUplifting_Produce
  examProduceDescriptionLabelId: Label_ExamUplifting_Exam
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamExtraTurn
  name: ターン追加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamExtraTurn
  examProduceDescriptionLabelId: Label_ExamExtraTurn
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamAntiDebuff
  name: 低下状態無効
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamAntiDebuff
  examProduceDescriptionLabelId: Label_ExamAntiDebuff
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaConsumptionAdd
  name: 消費体力増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaConsumptionAdd
  examProduceDescriptionLabelId: Label_ExamStaminaConsumptionAdd
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamBlockAddDown
  name: 不安
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamBlockAddDown
  examProduceDescriptionLabelId: Label_ExamBlockAddDown
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamBlockAddDownRestriction
  name: 不安無効
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamBlockAddDownRestriction
  examProduceDescriptionLabelId: Label_ExamBlockAddDownRestriction
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaRecoverAdd
  name: 体力回復効果増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaRecoverAdd
  examProduceDescriptionLabelId: Label_ExamStaminaRecoverAdd
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaReduceChange
  name: 体力消費軽減
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaReduceChange
  examProduceDescriptionLabelId: Label_ExamStaminaReduceChange
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamPanic
  name: 気まぐれ
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamPanic
  examProduceDescriptionLabelId: Label_ExamPanic
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonChangeSpecifyLessThan
  name: パラメータ上昇値変更
  produceDescriptionSwapId: Swap_Label_ExamLessonChangeSpecifyLessThan
  produceDescriptionLabelId: Label_ExamLessonChangeSpecifyLessThan
  examProduceDescriptionLabelId: Label_ExamLessonChangeSpecifyLessThan
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamHandHold
  name: 手札持ち越し
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamHandHold
  examProduceDescriptionLabelId: Label_ExamHandHold
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaConsumptionAddFix
  name: 消費体力追加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaConsumptionAddFix
  examProduceDescriptionLabelId: Label_ExamStaminaConsumptionAddFix
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaConsumptionAddDown
  name: 消費体力増加効果減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaConsumptionAddDown
  examProduceDescriptionLabelId: Label_ExamStaminaConsumptionAddDown
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaRecoverRestriction
  name: 体力回復無効
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaRecoverRestriction
  examProduceDescriptionLabelId: Label_ExamStaminaRecoverRestriction
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaConsumptionDownAdd
  name: 消費体力減少効果増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaConsumptionDownAdd
  examProduceDescriptionLabelId: Label_ExamStaminaConsumptionDownAdd
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamGetCardUpgrade
  name: 生成強化
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamGetCardUpgrade
  examProduceDescriptionLabelId: Label_ExamGetCardUpgrade
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamStaminaConsumptionDownFix
  name: 消費体力削減
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamStaminaConsumptionDownFix
  examProduceDescriptionLabelId: Label_ExamStaminaConsumptionDownFix
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamHandGraveCountCardDraw
  name: 手札を入れ替える
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamHandGraveCountCardDraw
  examProduceDescriptionLabelId: Label_ExamHandGraveCountCardDraw
  mainBuffMinThresholds: []
  noIcon: false
  noReference: true
- type: ProduceExamEffectType_ExamEffectTimer
  name: 発動予約
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamEffectTimer
  examProduceDescriptionLabelId: Label_ExamEffectTimer
  mainBuffMinThresholds: []
  noIcon: false
  noReference: true
- type: ProduceExamEffectType_ExamGimmickLessonDebuff
  name: 緊張
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamGimmickLessonDebuff_Produce
  examProduceDescriptionLabelId: Label_ExamGimmickLessonDebuff_Exam
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamGimmickParameterDebuff
  name: 不調
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamGimmickParameterDebuff
  examProduceDescriptionLabelId: Label_ExamGimmickParameterDebuff
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamGimmickSleepy
  name: 弱気
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamGimmickSleepy_Produce
  examProduceDescriptionLabelId: Label_ExamGimmickSleepy_Exam
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamGimmickEnthusiastic
  name: 熱意
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamGimmickEnthusiastic_Produce
  examProduceDescriptionLabelId: Label_ExamGimmickEnthusiastic_Exam
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamGimmickPlayCardLimit
  name: 使用不可
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamGimmickPlayCardLimit
  examProduceDescriptionLabelId: Label_ExamGimmickPlayCardLimit
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamGimmickSlump
  name: スランプ
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamGimmickSlump
  examProduceDescriptionLabelId: Label_ExamGimmickSlump
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamGimmickStartTurnCardDrawDown
  name: 手札減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamGimmickStartTurnCardDrawDown_Produce
  examProduceDescriptionLabelId: Label_ExamGimmickStartTurnCardDrawDown_Exam
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamBlockFix
  name: 固定元気
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamBlockFix
  examProduceDescriptionLabelId: Label_ExamBlockFix
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonChangeSpecifyMoreThan
  name: うわの空
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamLessonChangeSpecifyMoreThan
  examProduceDescriptionLabelId: Label_ExamLessonChangeSpecifyMoreThan
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamParameterBuffMultiplePerTurn
  name: 絶好調
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamParameterBuffMultiplePerTurn
  examProduceDescriptionLabelId: Label_ExamParameterBuffMultiplePerTurn
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_StanceLock
  name: 指針固定
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_StanceLock
  examProduceDescriptionLabelId: Label_StanceLock
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamDebuffRecover
  name: 低下状態回復
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamDebuffRecover
  examProduceDescriptionLabelId: Label_ExamDebuffRecover
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamReviewReduce
  name: 好印象減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamReviewReduce
  examProduceDescriptionLabelId: Label_ExamReviewReduce
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamAggressiveReduce
  name: やる気減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamAggressiveReduce
  examProduceDescriptionLabelId: Label_ExamAggressiveReduce
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonBuffReduce
  name: 集中減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamLessonBuffReduce
  examProduceDescriptionLabelId: Label_ExamLessonBuffReduce
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamParameterBuffReduce
  name: 好調減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamParameterBuffReduce
  examProduceDescriptionLabelId: Label_ExamParameterBuffReduce
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonValueMultipleDown
  name: パラメータ上昇量減少
  produceDescriptionSwapId: Swap_Label_ExamLessonValueMultipleDown
  produceDescriptionLabelId: Label_ExamLessonValueMultipleDown
  examProduceDescriptionLabelId: Label_ExamLessonValueMultipleDown
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamAddGrowEffect
  name: 成長
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamAddGrowEffect
  examProduceDescriptionLabelId: Label_ExamAddGrowEffect
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamOverPreservation
  name: のんびり
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamOverPreservation_Produce
  examProduceDescriptionLabelId: Label_ExamOverPreservation_Exam
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamEnthusiasticAdditive
  name: 熱意追加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamEnthusiasticAdditive
  examProduceDescriptionLabelId: Label_ExamEnthusiasticAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamEnthusiasticMultiple
  name: 熱意増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamEnthusiasticMultiple
  examProduceDescriptionLabelId: Label_ExamEnthusiasticMultiple
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamFullPowerLessonMultipleAdditive
  name: 全力強化
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamFullPowerLessonMultipleAdditive
  examProduceDescriptionLabelId: Label_ExamFullPowerLessonMultipleAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamConcentrationLessonMultipleAdditive
  name: 強気強化
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamConcentrationLessonMultipleAdditive
  examProduceDescriptionLabelId: Label_ExamConcentrationLessonMultipleAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonBuffAdditive
  name: 集中増加量増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamLessonBuffAdditive
  examProduceDescriptionLabelId: Label_ExamLessonBuffAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamParameterBuffAdditive
  name: 好調増加量増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamParameterBuffAdditive
  examProduceDescriptionLabelId: Label_ExamParameterBuffAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamAggressiveAdditive
  name: やる気増加量増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamAggressiveAdditive
  examProduceDescriptionLabelId: Label_ExamAggressiveAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamReviewAdditive
  name: 好印象増加量増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamReviewAdditive
  examProduceDescriptionLabelId: Label_ExamReviewAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamFullPowerPointAdditive
  name: 全力値増加量増加
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamFullPowerPointAdditive
  examProduceDescriptionLabelId: Label_ExamFullPowerPointAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamGrowEffectLessonAddAdditive
  name: パラメータ上昇値増加量増加
  produceDescriptionSwapId: Swap_Label_ExamGrowEffectLessonAddAdditive
  produceDescriptionLabelId: Label_ExamGrowEffectLessonAddAdditive
  examProduceDescriptionLabelId: Label_ExamGrowEffectLessonAddAdditive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamParameterBuffMultiplePerTurnReduce
  name: 好調減少
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamParameterBuffMultiplePerTurnReduce
  examProduceDescriptionLabelId: Label_ExamParameterBuffMultiplePerTurnReduce
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamLessonValueMultipleDependReviewOrAggressive
  name: プライド
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamLessonValueMultipleDependReviewOrAggressive
  examProduceDescriptionLabelId: Label_ExamLessonValueMultipleDependReviewOrAggressive
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false
- type: ProduceExamEffectType_ExamReviewMultiple
  name: 好印象強化
  produceDescriptionSwapId: ""
  produceDescriptionLabelId: Label_ExamReviewMultiple
  examProduceDescriptionLabelId: Label_ExamReviewMultiple
  mainBuffMinThresholds: []
  noIcon: false
  noReference: false