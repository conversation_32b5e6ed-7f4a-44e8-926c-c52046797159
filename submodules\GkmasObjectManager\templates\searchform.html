<script
    type="text/javascript"
    src="{{ url_for('static', filename='js/searchform.js') }}"
></script>

<form id="searchForm" class="position-relative">
    <input
        id="searchInput"
        class="form-control"
        type="search"
        placeholder="Search..."
    />
    <button
        id="searchButton"
        class="bi bi-search gentle-button floating-button"
        type="submit"
    ></button>
    <div id="filtersMenu" class="text-size-125-force hide-by-default">
        <div class="row mb-1">
            <div class="col-md-12">By media</div>
        </div>
        <div class="row gx-2" id="filtersMediaContainer"></div>
        <div class="row mt-2 mb-1">
            <div class="col-md-12">By character</div>
        </div>
        <div class="row gx-0" id="filtersCharacterContainer"></div>
        <div class="row mt-3 mb-1">
            <div class="col-md-12">By subtype</div>
        </div>
        <div class="row gx-2" id="filtersSubtypeContainer"></div>
        <div class="row mt-2 mb-1">
            <div class="col-md-12">By rarity</div>
        </div>
        <div class="row gx-2" id="filtersRarityContainer"></div>
        <div class="row mt-2 mb-1">
            <div class="col-md-12">By song/event</div>
        </div>
        <div
            class="row gx-2 text-size-75-force"
            id="filtersSongContainer"
        ></div>
    </div>
</form>
